"""واجهة برمجة التطبيق (API) لتطبيق Ta9affi"""

from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from app.models import Subject, Domain, KnowledgeMaterial, Competency, LevelDatabase, LevelDataEntry
from app import db

# إنشاء بلوبرنت للـ API
api_bp = Blueprint('api', __name__)

@api_bp.route('/api/subjects/<int:level_id>')
@login_required
def get_subjects(level_id):
    """
    الحصول على المواد الدراسية لمستوى تعليمي معين
    """
    subjects = Subject.query.filter_by(level_id=level_id).all()
    return jsonify([
        {'id': subject.id, 'name': subject.name}
        for subject in subjects
    ])

@api_bp.route('/api/domains/<int:subject_id>')
@login_required
def get_domains(subject_id):
    """
    الحصول على الميادين لمادة دراسية معينة
    """
    domains = Domain.query.filter_by(subject_id=subject_id).all()
    return jsonify([
        {'id': domain.id, 'name': domain.name}
        for domain in domains
    ])

@api_bp.route('/api/materials/<int:domain_id>')
@login_required
def get_materials(domain_id):
    """
    الحصول على المواد المعرفية لميدان معين
    """
    materials = KnowledgeMaterial.query.filter_by(domain_id=domain_id).all()
    return jsonify([
        {'id': material.id, 'name': material.name}
        for material in materials
    ])

@api_bp.route('/api/competencies/<int:material_id>')
@login_required
def get_competencies(material_id):
    """
    الحصول على الكفاءات المستهدفة لمادة معرفية معينة
    """
    competencies = Competency.query.filter_by(knowledge_material_id=material_id).all()
    return jsonify([
        {'id': competency.id, 'description': competency.description}
        for competency in competencies
    ])

@api_bp.route('/api/level_database_entries/<int:database_id>')
@login_required
def get_level_database_entries(database_id):
    """
    الحصول على بيانات قاعدة بيانات مستوى معين
    """
    entries = LevelDataEntry.query.filter_by(database_id=database_id).all()
    return jsonify([
        {
            'id': entry.id,
            'entry_type': entry.entry_type,
            'parent_id': entry.parent_id,
            'name': entry.name,
            'description': entry.description,
            'order_num': entry.order_num,
            'is_active': entry.is_active
        }
        for entry in entries
    ])

@api_bp.route('/api/level_database_entries/<int:database_id>/<string:entry_type>')
@login_required
def get_level_database_entries_by_type(database_id, entry_type):
    """
    الحصول على بيانات قاعدة بيانات مستوى معين حسب النوع
    """
    entries = LevelDataEntry.query.filter_by(database_id=database_id, entry_type=entry_type).all()
    return jsonify([
        {
            'id': entry.id,
            'entry_type': entry.entry_type,
            'parent_id': entry.parent_id,
            'name': entry.name,
            'description': entry.description,
            'order_num': entry.order_num,
            'is_active': entry.is_active
        }
        for entry in entries
    ])

@api_bp.route('/api/level_database_entries/<int:database_id>/<string:entry_type>/<int:parent_id>')
@login_required
def get_level_database_entries_by_parent(database_id, entry_type, parent_id):
    """
    الحصول على بيانات قاعدة بيانات مستوى معين حسب النوع والأب
    """
    entries = LevelDataEntry.query.filter_by(
        database_id=database_id,
        entry_type=entry_type,
        parent_id=parent_id
    ).all()
    return jsonify([
        {
            'id': entry.id,
            'entry_type': entry.entry_type,
            'parent_id': entry.parent_id,
            'name': entry.name,
            'description': entry.description,
            'order_num': entry.order_num,
            'is_active': entry.is_active
        }
        for entry in entries
    ])

@api_bp.route('/api/add_level_database_entry', methods=['POST'])
@login_required
def add_level_database_entry():
    """
    إضافة عنصر جديد إلى قاعدة بيانات مستوى
    """
    data = request.json
    
    # التحقق من وجود البيانات المطلوبة
    required_fields = ['database_id', 'entry_type', 'name']
    if not all(field in data for field in required_fields):
        return jsonify({'error': 'البيانات غير مكتملة'}), 400
    
    # إنشاء عنصر جديد
    new_entry = LevelDataEntry(
        database_id=data['database_id'],
        entry_type=data['entry_type'],
        parent_id=data.get('parent_id'),
        name=data['name'],
        description=data.get('description'),
        order_num=data.get('order_num', 0),
        is_active=data.get('is_active', True),
        extra_data=data.get('extra_data')
    )
    
    db.session.add(new_entry)
    db.session.commit()
    
    return jsonify({
        'id': new_entry.id,
        'message': 'تم إضافة العنصر بنجاح'
    }), 201

@api_bp.route('/api/update_level_database_entry/<int:entry_id>', methods=['PUT'])
@login_required
def update_level_database_entry(entry_id):
    """
    تحديث عنصر في قاعدة بيانات مستوى
    """
    entry = LevelDataEntry.query.get_or_404(entry_id)
    data = request.json
    
    # تحديث البيانات
    if 'name' in data:
        entry.name = data['name']
    if 'description' in data:
        entry.description = data['description']
    if 'parent_id' in data:
        entry.parent_id = data['parent_id']
    if 'order_num' in data:
        entry.order_num = data['order_num']
    if 'is_active' in data:
        entry.is_active = data['is_active']
    if 'extra_data' in data:
        entry.extra_data = data['extra_data']
    
    db.session.commit()
    
    return jsonify({
        'message': 'تم تحديث العنصر بنجاح'
    })

@api_bp.route('/api/delete_level_database_entry/<int:entry_id>', methods=['DELETE'])
@login_required
def delete_level_database_entry(entry_id):
    """
    حذف عنصر من قاعدة بيانات مستوى
    """
    entry = LevelDataEntry.query.get_or_404(entry_id)
    
    # التحقق من عدم وجود عناصر فرعية
    child_entries = LevelDataEntry.query.filter_by(parent_id=entry_id).first()
    if child_entries:
        return jsonify({
            'error': 'لا يمكن حذف هذا العنصر لأنه يحتوي على عناصر فرعية'
        }), 400
    
    db.session.delete(entry)
    db.session.commit()
    
    return jsonify({
        'message': 'تم حذف العنصر بنجاح'
    })
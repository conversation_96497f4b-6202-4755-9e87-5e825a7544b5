#!/usr/bin/env python3
"""
أداة لإنشاء مفتاح سري قوي لتطبيق Ta9affi
"""

import secrets
import os

def generate_secret_key():
    """إنشاء مفتاح سري قوي"""
    return secrets.token_hex(32)

def save_secret_key_to_env():
    """حفظ المفتاح السري في ملف .env"""
    secret_key = generate_secret_key()
    
    env_content = f"""# إعدادات الأمان لتطبيق Ta9affi
SECRET_KEY={secret_key}

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///ta9affi_new.db

# إعدادات الأمان
WTF_CSRF_ENABLED=True
WTF_CSRF_TIME_LIMIT=3600

# إعدادات التطوير
FLASK_ENV=development
FLASK_DEBUG=True
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ تم إنشاء ملف .env بنجاح!")
    print(f"🔑 المفتاح السري: {secret_key}")
    print("\n⚠️  تأكد من إضافة .env إلى ملف .gitignore لحماية المفتاح السري")

if __name__ == '__main__':
    save_secret_key_to_env()

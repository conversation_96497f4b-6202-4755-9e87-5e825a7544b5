"""مسارات المدير لتطبيق Ta9affi"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.models import User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, LevelDatabase, AdminInspectorNotification
from app import db
from app.utils.decorators import admin_required

# إنشاء بلوبرنت للمدير
admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """
    لوحة تحكم المدير
    """
    # إحصائيات للوحة التحكم
    inspectors_count = User.query.filter_by(role=Role.INSPECTOR).count()
    levels_count = EducationalLevel.query.count()
    subjects_count = Subject.query.count()
    competencies_count = Competency.query.count()

    # قائمة المفتشين
    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()

    return render_template('admin_dashboard.html',
                           inspectors_count=inspectors_count,
                           levels_count=levels_count,
                           subjects_count=subjects_count,
                           competencies_count=competencies_count,
                           inspectors=inspectors)

@admin_bp.route('/manage_users')
@login_required
@admin_required
def manage_users():
    """
    إدارة المستخدمين
    """
    # جلب جميع المستخدمين مصنفين حسب الدور
    admins = User.query.filter_by(role=Role.ADMIN).all()
    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()
    teachers = User.query.filter_by(role=Role.TEACHER).all()

    return render_template('admin/manage_users.html',
                           admins=admins,
                           inspectors=inspectors,
                           teachers=teachers)

@admin_bp.route('/admin/add_user', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """
    إضافة مستخدم جديد
    """
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role')

        # التحقق من عدم وجود مستخدم بنفس اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter((User.username == username) | (User.email == email)).first()
        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل.', 'danger')
            return redirect(url_for('admin.add_user'))

        # إنشاء مستخدم جديد
        new_user = User(username=username, email=email, role=role)
        new_user.set_password(password)

        # حفظ المستخدم في قاعدة البيانات
        db.session.add(new_user)
        db.session.commit()

        flash(f'تم إنشاء المستخدم {username} بنجاح!', 'success')
        return redirect(url_for('admin.manage_users'))

    return render_template('admin/add_user.html')

@admin_bp.route('/admin/edit_user/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """
    تعديل بيانات مستخدم
    """
    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        role = request.form.get('role')
        new_password = request.form.get('new_password')

        # التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter(
            (User.username == username) | (User.email == email),
            User.id != user_id
        ).first()

        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل.', 'danger')
            return redirect(url_for('admin.edit_user', user_id=user_id))

        # تحديث بيانات المستخدم
        user.username = username
        user.email = email
        user.role = role

        # تحديث كلمة المرور إذا تم توفيرها
        if new_password:
            user.set_password(new_password)

        db.session.commit()
        flash('تم تحديث بيانات المستخدم بنجاح!', 'success')
        return redirect(url_for('admin.manage_users'))

    return render_template('admin/edit_user.html', user=user)

@admin_bp.route('/admin/delete_user/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """
    حذف مستخدم
    """
    user = User.query.get_or_404(user_id)

    # لا يمكن للمستخدم حذف نفسه
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص.', 'danger')
        return redirect(url_for('admin.manage_users'))

    db.session.delete(user)
    db.session.commit()
    flash('تم حذف المستخدم بنجاح!', 'success')
    return redirect(url_for('admin.manage_users'))

@admin_bp.route('/manage_levels')
@login_required
@admin_required
def manage_levels():
    """
    إدارة المستويات التعليمية
    """
    levels = EducationalLevel.query.all()
    return render_template('admin/manage_levels.html', levels=levels)

@admin_bp.route('/add_level', methods=['GET', 'POST'])
@login_required
@admin_required
def add_level():
    """
    إضافة مستوى تعليمي جديد
    """
    if request.method == 'POST':
        name = request.form.get('name')
        database_prefix = request.form.get('database_prefix')

        # التحقق من عدم وجود مستوى بنفس الاسم
        existing_level = EducationalLevel.query.filter_by(name=name).first()
        if existing_level:
            flash('يوجد بالفعل مستوى تعليمي بهذا الاسم.', 'danger')
            return redirect(url_for('admin.add_level'))

        # إنشاء مستوى جديد
        new_level = EducationalLevel(name=name, database_prefix=database_prefix)
        db.session.add(new_level)
        db.session.commit()

        flash(f'تم إنشاء المستوى التعليمي {name} بنجاح!', 'success')
        return redirect(url_for('admin.manage_levels'))

    return render_template('admin/add_level.html')

@admin_bp.route('/admin/edit_level/<int:level_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_level(level_id):
    """
    تعديل مستوى تعليمي
    """
    level = EducationalLevel.query.get_or_404(level_id)

    if request.method == 'POST':
        name = request.form.get('name')
        database_prefix = request.form.get('database_prefix')
        is_active = 'is_active' in request.form

        # التحقق من عدم وجود مستوى آخر بنفس الاسم
        existing_level = EducationalLevel.query.filter(
            EducationalLevel.name == name,
            EducationalLevel.id != level_id
        ).first()

        if existing_level:
            flash('يوجد بالفعل مستوى تعليمي بهذا الاسم.', 'danger')
            return redirect(url_for('admin.edit_level', level_id=level_id))

        # تحديث بيانات المستوى
        level.name = name
        level.database_prefix = database_prefix
        level.is_active = is_active

        db.session.commit()
        flash('تم تحديث بيانات المستوى التعليمي بنجاح!', 'success')
        return redirect(url_for('admin.manage_levels'))

    return render_template('admin/edit_level.html', level=level)

@admin_bp.route('/admin/delete_level/<int:level_id>', methods=['POST'])
@login_required
@admin_required
def delete_level(level_id):
    """
    حذف مستوى تعليمي
    """
    level = EducationalLevel.query.get_or_404(level_id)

    # التحقق من عدم وجود مواد مرتبطة بهذا المستوى
    if level.subjects:
        flash('لا يمكن حذف هذا المستوى لأنه يحتوي على مواد دراسية.', 'danger')
        return redirect(url_for('admin.manage_levels'))

    db.session.delete(level)
    db.session.commit()
    flash('تم حذف المستوى التعليمي بنجاح!', 'success')
    return redirect(url_for('admin.manage_levels'))

@admin_bp.route('/manage_level_databases')
@login_required
@admin_required
def manage_level_databases():
    """
    إدارة قواعد بيانات المستويات
    """
    levels = EducationalLevel.query.all()
    databases = LevelDatabase.query.all()
    return render_template('manage_level_databases.html', levels=levels, databases=databases)

@admin_bp.route('/add_database', methods=['POST'])
@login_required
@admin_required
def add_database():
    """
    إضافة قاعدة بيانات جديدة لمستوى تعليمي
    """
    level_id = request.form.get('level_id')
    name = request.form.get('name')
    file_path = request.form.get('file_path', '')

    # التحقق من وجود المستوى
    level = EducationalLevel.query.get_or_404(level_id)

    # التحقق من عدم وجود قاعدة بيانات بنفس الاسم لنفس المستوى
    existing_db = LevelDatabase.query.filter_by(level_id=level_id, name=name).first()
    if existing_db:
        flash('يوجد بالفعل قاعدة بيانات بهذا الاسم لهذا المستوى.', 'danger')
        return redirect(url_for('admin.manage_level_databases'))

    # إنشاء قاعدة بيانات جديدة
    new_db = LevelDatabase(level_id=level_id, name=name, file_path=file_path)
    db.session.add(new_db)
    db.session.commit()

    flash(f'تم إنشاء قاعدة البيانات {name} بنجاح!', 'success')
    return redirect(url_for('admin.manage_level_databases'))

@admin_bp.route('/toggle_database/<int:database_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def toggle_database(database_id):
    """
    تفعيل/تعطيل قاعدة بيانات
    """
    database = LevelDatabase.query.get_or_404(database_id)
    database.is_active = not database.is_active
    db.session.commit()

    status = 'تفعيل' if database.is_active else 'تعطيل'
    flash(f'تم {status} قاعدة البيانات بنجاح!', 'success')
    return redirect(url_for('admin.manage_level_databases'))

@admin_bp.route('/delete_database/<int:database_id>', methods=['POST'])
@login_required
@admin_required
def delete_database(database_id):
    """
    حذف قاعدة بيانات
    """
    database = LevelDatabase.query.get_or_404(database_id)

    # التحقق من عدم وجود بيانات مرتبطة بقاعدة البيانات
    if database.data_entries:
        flash('لا يمكن حذف قاعدة البيانات لأنها تحتوي على بيانات.', 'danger')
        return redirect(url_for('admin.manage_level_databases'))

    db.session.delete(database)
    db.session.commit()
    flash('تم حذف قاعدة البيانات بنجاح!', 'success')
    return redirect(url_for('admin.manage_level_databases'))

@admin_bp.route('/notifications')
@login_required
@admin_required
def notifications():
    """
    إدارة الإشعارات بين الإدارة والمفتشين
    """
    page = request.args.get('page', 1, type=int)
    notifications = AdminInspectorNotification.query.filter_by(sender_id=current_user.id).order_by(AdminInspectorNotification.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False)

    # الحصول على قائمة المفتشين
    inspectors = User.query.filter_by(role='inspector').all()

    return render_template('admin_notifications.html', notifications=notifications, inspectors=inspectors)

@admin_bp.route('/notifications/send', methods=['POST'])
@login_required
@admin_required
def send_notification():
    """
    إرسال إشعار من الإدارة إلى المفتش
    """
    receiver_id = request.form.get('receiver_id')
    title = request.form.get('title')
    message = request.form.get('message')

    if not receiver_id or not title or not message:
        flash('جميع الحقول مطلوبة', 'danger')
        return redirect(url_for('admin.notifications'))

    # التحقق من أن المستلم هو مفتش
    receiver = User.query.get(receiver_id)
    if not receiver or receiver.role != 'inspector':
        flash('المستلم غير صالح', 'danger')
        return redirect(url_for('admin.notifications'))

    # إنشاء الإشعار
    notification = AdminInspectorNotification(
        sender_id=current_user.id,
        receiver_id=receiver_id,
        title=title,
        message=message
    )
    db.session.add(notification)
    db.session.commit()

    flash('تم إرسال الإشعار بنجاح!', 'success')
    return redirect(url_for('admin.notifications'))

@admin_bp.route('/manage_databases')
@login_required
@admin_required
def manage_databases():
    """
    إدارة قواعد البيانات المنفصلة
    """
    levels = EducationalLevel.query.all()
    databases = LevelDatabase.query.all()
    return render_template('manage_level_databases.html', levels=levels, databases=databases)

@admin_bp.route('/view_database/<int:db_id>')
@login_required
@admin_required
def view_database(db_id):
    """
    عرض محتوى قاعدة بيانات
    """
    database = LevelDatabase.query.get_or_404(db_id)
    # يمكن إضافة منطق عرض محتوى قاعدة البيانات هنا
    return render_template('view_database.html', database=database)

@admin_bp.route('/edit_database/<int:db_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_database(db_id):
    """
    تعديل قاعدة بيانات
    """
    database = LevelDatabase.query.get_or_404(db_id)

    if request.method == 'POST':
        database.name = request.form.get('name')
        database.file_path = request.form.get('file_path')
        database.is_active = 'is_active' in request.form

        db.session.commit()
        flash('تم تحديث قاعدة البيانات بنجاح!', 'success')
        return redirect(url_for('admin.manage_databases'))

    return render_template('edit_database.html', database=database)

@admin_bp.route('/export_database_data/<int:db_id>')
@login_required
@admin_required
def export_database_data(db_id):
    """
    تصدير بيانات قاعدة البيانات إلى Excel
    """
    database = LevelDatabase.query.get_or_404(db_id)
    # يمكن إضافة منطق التصدير هنا
    flash('ميزة التصدير قيد التطوير.', 'info')
    return redirect(url_for('admin.manage_databases'))

@admin_bp.route('/cleanup_inactive_levels')
@login_required
@admin_required
def cleanup_inactive_levels():
    """
    حذف المستويات غير النشطة
    """
    # يمكن إضافة منطق حذف المستويات غير النشطة هنا
    flash('ميزة حذف المستويات غير النشطة قيد التطوير.', 'info')
    return redirect(url_for('main.teaching_program'))

@admin_bp.route('/delete_database_entry/<int:db_id>/<int:entry_id>', methods=['POST'])
@login_required
@admin_required
def delete_database_entry(db_id, entry_id):
    """
    حذف عنصر من قاعدة البيانات
    """
    database = LevelDatabase.query.get_or_404(db_id)
    # يمكن إضافة منطق حذف العنصر هنا
    flash('ميزة حذف العناصر قيد التطوير.', 'info')
    return redirect(url_for('admin.view_database', db_id=db_id))

@admin_bp.route('/import_database_data/<int:db_id>', methods=['POST'])
@login_required
@admin_required
def import_database_data(db_id):
    """
    استيراد بيانات إلى قاعدة البيانات من ملف Excel
    """
    database = LevelDatabase.query.get_or_404(db_id)
    # يمكن إضافة منطق الاستيراد هنا
    flash('ميزة الاستيراد قيد التطوير.', 'info')
    return redirect(url_for('admin.view_database', db_id=db_id))

@admin_bp.route('/add_database_entry/<int:db_id>', methods=['POST'])
@login_required
@admin_required
def add_database_entry(db_id):
    """
    إضافة عنصر جديد إلى قاعدة البيانات
    """
    database = LevelDatabase.query.get_or_404(db_id)
    # يمكن إضافة منطق إضافة العنصر هنا
    flash('ميزة إضافة العناصر قيد التطوير.', 'info')
    return redirect(url_for('admin.view_database', db_id=db_id))

@admin_bp.route('/export_all_databases')
@login_required
@admin_required
def export_all_databases():
    """
    تصدير جميع قواعد البيانات إلى ملف Excel
    """
    # يمكن إضافة منطق تصدير جميع قواعد البيانات هنا
    flash('ميزة تصدير جميع قواعد البيانات قيد التطوير.', 'info')
    return redirect(url_for('admin.manage_databases'))

@admin_bp.route('/export_data/<string:model_name>')
@login_required
@admin_required
def export_data(model_name):
    """
    تصدير بيانات نموذج معين إلى ملف Excel
    """
    # يمكن إضافة منطق تصدير البيانات هنا
    flash(f'ميزة تصدير {model_name} قيد التطوير.', 'info')
    return redirect(url_for('admin.manage_databases'))

@admin_bp.route('/import_all_levels_page')
@login_required
@admin_required
def import_all_levels_page():
    """
    صفحة استيراد جميع المستويات
    """
    # يمكن إضافة منطق عرض صفحة الاستيراد هنا
    flash('ميزة استيراد جميع المستويات قيد التطوير.', 'info')
    return redirect(url_for('admin.manage_databases'))

@admin_bp.route('/generate_primary_database')
@login_required
@admin_required
def generate_primary_database():
    """
    إنشاء قاعدة بيانات التعليم الابتدائي
    """
    # يمكن إضافة منطق إنشاء قاعدة بيانات التعليم الابتدائي هنا
    flash('ميزة إنشاء قاعدة بيانات التعليم الابتدائي قيد التطوير.', 'info')
    return redirect(url_for('admin.manage_databases'))





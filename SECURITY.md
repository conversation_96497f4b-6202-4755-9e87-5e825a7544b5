# 🔒 دليل الأمان - تطبيق Ta9affi

## 🛡️ التحسينات الأمنية المطبقة

### 1. **تشفير كلمات المرور**
- ✅ استخدام `werkzeug.security` لتشفير كلمات المرور
- ✅ تطبيق `generate_password_hash()` و `check_password_hash()`
- ✅ عدم تخزين كلمات المرور بشكل واضح

### 2. **حماية CSRF**
- ✅ تطبيق `Flask-WTF` للحماية من هجمات CSRF
- ✅ إضافة CSRF tokens في جميع النماذج
- ✅ تحديد مهلة زمنية للـ tokens (ساعة واحدة)

### 3. **تحديد معدل الطلبات**
- ✅ استخدام `Flask-Limiter` لمنع الهجمات
- ✅ حد أقصى 5 محاولات تسجيل دخول في الدقيقة
- ✅ حد أقصى 3 تسجيلات جديدة في الساعة
- ✅ حد عام 200 طلب يومياً و 50 طلب في الساعة

### 4. **رؤوس الأمان**
- ✅ استخدام `Flask-Talisman` لإضافة رؤوس الأمان
- ✅ تطبيق Content Security Policy (CSP)
- ✅ حماية من XSS و Clickjacking

### 5. **التحقق من قوة كلمات المرور**
- ✅ 8 أحرف على الأقل
- ✅ حرف كبير واحد على الأقل
- ✅ حرف صغير واحد على الأقل
- ✅ رقم واحد على الأقل

### 6. **تنظيف البيانات المدخلة**
- ✅ تنظيف جميع المدخلات من الأحرف الخطيرة
- ✅ استخدام `html.escape()` لمنع XSS

### 7. **تسجيل الأحداث الأمنية**
- ✅ تسجيل محاولات تسجيل الدخول الناجحة والفاشلة
- ✅ تسجيل أخطاء CSRF
- ✅ تسجيل تجاوز حدود معدل الطلبات
- ✅ حفظ السجلات في ملف `security.log`

### 8. **إدارة المفاتيح السرية**
- ✅ إنشاء مفاتيح سرية قوية باستخدام `secrets`
- ✅ استخدام متغيرات البيئة لحفظ المفاتيح
- ✅ حماية الملفات الحساسة في `.gitignore`

## 🚀 كيفية تطبيق الأمان

### 1. تثبيت المتطلبات الجديدة:
```bash
pip install -r requirements.txt
```

### 2. إنشاء مفتاح سري قوي:
```bash
python generate_secret_key.py
```

### 3. تشغيل التطبيق:
```bash
python app.py
```

## ⚠️ تحذيرات أمنية

### للإنتاج:
1. **تغيير المفتاح السري** - استخدم مفتاح فريد وقوي
2. **تفعيل HTTPS** - قم بتعديل `force_https=True` في Talisman
3. **استخدام قاعدة بيانات آمنة** - PostgreSQL أو MySQL بدلاً من SQLite
4. **تحديث المكتبات** - تأكد من استخدام أحدث الإصدارات
5. **مراقبة السجلات** - راقب ملف `security.log` بانتظام

### كلمات المرور الافتراضية:
- **المدير**: admin / Admin123!
- **المفتش**: inspector / Inspector123!
- **الأستاذ**: teacher / Teacher123!

⚠️ **يجب تغيير هذه كلمات المرور فوراً في الإنتاج!**

## 📋 قائمة مراجعة الأمان

- [ ] تغيير كلمات المرور الافتراضية
- [ ] إنشاء مفتاح سري جديد للإنتاج
- [ ] تفعيل HTTPS
- [ ] مراجعة إعدادات CSP
- [ ] إعداد نسخ احتياطية آمنة
- [ ] تحديث جميع المكتبات
- [ ] مراقبة السجلات الأمنية
- [ ] اختبار الثغرات الأمنية

## 🔍 مراقبة الأمان

### ملف السجلات:
- **الموقع**: `security.log`
- **المحتوى**: محاولات تسجيل الدخول، أخطاء CSRF، تجاوز الحدود
- **التنسيق**: `التاريخ - المستوى - الرسالة`

### مراقبة مستمرة:
```bash
tail -f security.log
```

## 📞 الإبلاغ عن الثغرات

إذا اكتشفت ثغرة أمنية، يرجى الإبلاغ عنها فوراً عبر:
- البريد الإلكتروني: <EMAIL>
- أو إنشاء issue في المستودع مع تصنيف "security"

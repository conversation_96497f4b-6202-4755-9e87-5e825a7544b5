{% extends "base.html" %}

{% block title %}إدارة قواعد البيانات للمستويات التعليمية{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4">إدارة قواعد البيانات للمستويات التعليمية</h1>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">قاعدة بيانات التعليم الإبتدائي</h5>
        </div>
        <div class="card-body">
            <p>إنشاء قواعد بيانات للمستويات الخمسة للتعليم الإبتدائي مع المواد الدراسية والميادين.</p>
            <form action="{{ url_for('create_primary_databases') }}" method="POST">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-database"></i> إنشاء قواعد بيانات التعليم الإبتدائي
                </button>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">قواعد البيانات الحالية</h5>
        </div>
        <div class="card-body">
            {% if databases %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>المستوى التعليمي</th>
                            <th>مسار الملف</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for database in databases %}
                        <tr>
                            <td>{{ database.name }}</td>
                            <td>{{ database.level.name if database.level else 'غير محدد' }}</td>
                            <td>{{ database.file_path }}</td>
                            <td>
                                {% if database.is_active %}
                                <span class="badge bg-success">مفعل</span>
                                {% else %}
                                <span class="badge bg-danger">معطل</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_database', db_id=database.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="{{ url_for('edit_database', db_id=database.id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <form action="{{ url_for('toggle_database', database_id=database.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm {% if database.is_active %}btn-danger{% else %}btn-success{% endif %}">
                                            <i class="fas {% if database.is_active %}fa-ban{% else %}fa-check{% endif %}"></i>
                                            {% if database.is_active %}تعطيل{% else %}تفعيل{% endif %}
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                لا توجد قواعد بيانات حال<|im_start|>.
            </div>
            {% endif %}

            <a href="{{ url_for('create_database') }}" class="btn btn-success mt-3">
                <i class="fas fa-plus"></i> إنشاء قاعدة بيانات جديدة
            </a>
        </div>
    </div>
</div>
{% endblock %}
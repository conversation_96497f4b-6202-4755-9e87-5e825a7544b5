from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from enum import Enum

db = SQLAlchemy()

class Role(Enum):
    ADMIN = 'admin'
    INSPECTOR = 'inspector'
    TEACHER = 'teacher'

# جدول العلاقة بين المفتشين والأساتذة
inspector_teacher = db.Table('inspector_teacher',
    db.<PERSON>umn('inspector_id', db.Integer, db.<PERSON><PERSON>('user.id'), primary_key=True),
    db.Column('teacher_id', db.Integer, db.<PERSON><PERSON>ey('user.id'), primary_key=True)
)

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)
    role = db.Column(db.Enum(Role), nullable=False, default=Role.TEACHER)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات للمفتشين والأساتذة
    supervised_teachers = db.relationship('User', 
                                        secondary=inspector_teacher,
                                        primaryjoin=id == inspector_teacher.c.inspector_id,
                                        secondaryjoin=id == inspector_teacher.c.teacher_id,
                                        backref='supervisors',
                                        lazy='dynamic')

class EducationalLevel(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    is_active = db.Column(db.Boolean, default=True)
    database_prefix = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class LevelDatabase(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    file_path = db.Column(db.String(200), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    level = db.relationship('EducationalLevel', backref=db.backref('databases', lazy=True))

class LevelDataEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    database_id = db.Column(db.Integer, db.ForeignKey('level_database.id'), nullable=False)
    entry_type = db.Column(db.String(50), nullable=False)  # subject, domain, material, competency
    parent_id = db.Column(db.Integer, db.ForeignKey('level_data_entry.id'), nullable=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    database = db.relationship('LevelDatabase', backref=db.backref('entries', lazy=True))
    parent = db.relationship('LevelDataEntry', remote_side=[id], backref='children')

class Subject(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    level_id = db.Column(db.Integer, db.ForeignKey('educational_level.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    level = db.relationship('EducationalLevel', backref=db.backref('subjects', lazy=True))

class Domain(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    subject = db.relationship('Subject', backref=db.backref('domains', lazy=True))

class KnowledgeMaterial(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    domain_id = db.Column(db.Integer, db.ForeignKey('domain.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    domain = db.relationship('Domain', backref=db.backref('materials', lazy=True))

class Competency(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    material_id = db.Column(db.Integer, db.ForeignKey('knowledge_material.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    material = db.relationship('KnowledgeMaterial', backref=db.backref('competencies', lazy=True))

class Schedule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    competency_id = db.Column(db.Integer, db.ForeignKey('competency.id'), nullable=False)
    scheduled_date = db.Column(db.Date, nullable=False)
    notes = db.Column(db.Text)
    is_completed = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user = db.relationship('User', backref=db.backref('schedules', lazy=True))
    competency = db.relationship('Competency', backref=db.backref('schedules', lazy=True))

class ProgressEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    competency_id = db.Column(db.Integer, nullable=True)
    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False)  # completed, in_progress, planned
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user = db.relationship('User', backref=db.backref('progress_entries', lazy=True))

class AdminInspectorNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_admin_notifications')
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref='received_admin_notifications')

class InspectorTeacherNotification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_inspector_notifications')
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref='received_inspector_notifications')

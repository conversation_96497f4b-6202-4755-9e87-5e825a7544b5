# Ta9affi - نظام إدارة التعليم الابتدائي

نظام شامل لإدارة التعليم الابتدائي في الجزائر، يوفر أدوات لإدارة المناهج والمواد التعليمية والتقييم.

## التشغيل السريع

### 1. تشغيل التطبيق
```bash
python app.py
```

### 2. الوصول للتطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

### 3. تسجيل الدخول
- **اسم المستخدم**: admin
- **كلمة المرور**: Admin123!

## إنشاء قاعدة بيانات التعليم الابتدائي

1. سجل الدخول كمدير
2. انتقل إلى "إدارة قواعد البيانات"
3. اضغط على "إنشاء قاعدة بيانات التعليم الابتدائي"
4. سيتم إنشاء 5 مستويات تعليمية مع 13 مادة دراسية لكل مستوى

### المواد الدراسية المدعومة

- اللغة العربية (20 ميدان)
- الرياضيات (7 ميادين)
- التربية الإسلامية (6 ميادين)
- التربية العلمية (5 ميادين)
- التربية المدنية (4 ميادين)
- التربية الفنية/التشكيلية (6 ميادين)
- التاريخ (4 ميادين)
- الجغرافيا (4 ميادين)
- التربية البدنية (4 ميادين)
- الأمازيغية
- الفرنسية
- حفظ القرآن
- الإنجليزية

## هيكل المشروع النظيف

```
Ta9affi/
├── app.py                 # التطبيق الرئيسي الوحيد
├── requirements.txt       # متطلبات Python المبسطة
├── run.bat               # ملف تشغيل Windows
├── static/               # الملفات الثابتة
├── templates/            # قوالب HTML
├── instance/             # قواعد البيانات
└── venv/                # البيئة الافتراضية
```

---

**تم تطوير هذا النظام لخدمة التعليم الابتدائي في الجزائر**

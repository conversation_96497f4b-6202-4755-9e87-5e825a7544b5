"""المسارات الرئيسية لتطبيق Ta9affi"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.models import Role, EducationalLevel, LevelDatabase, AdminInspectorNotification, InspectorTeacherNotification
from app import db

# إنشاء بلوبرنت للمسارات الرئيسية
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """
    الصفحة الرئيسية
    """
    # إذا كان المستخدم مسجل دخوله، توجيهه إلى لوحة التحكم
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    # عرض صفحة الترحيب للزوار
    return render_template('index.html')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """
    لوحة التحكم - توجيه المستخدم إلى لوحة التحكم المناسبة حسب دوره
    """
    # توجيه المستخدم إلى لوحة التحكم المناسبة حسب دوره
    if current_user.is_admin():
        return redirect(url_for('admin.dashboard'))
    elif current_user.is_inspector():
        return redirect(url_for('inspector.dashboard'))
    elif current_user.is_teacher():
        return redirect(url_for('teacher.dashboard'))
    else:
        flash('دور المستخدم غير معروف.', 'danger')
        return redirect(url_for('auth.logout'))

@main_bp.route('/profile')
@login_required
def profile():
    """
    صفحة الملف الشخصي للمستخدم
    """
    return render_template('profile.html')

@main_bp.route('/teaching_program')
@login_required
def teaching_program():
    """
    عرض البرنامج السنوي للتدريس
    """
    # جلب جميع المستويات التعليمية
    levels = EducationalLevel.query.all()

    # الحصول على قواعد البيانات النشطة لكل مستوى
    level_databases = {}
    for level in levels:
        database = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
        if database:
            level_databases[level.id] = database.id

    return render_template('teaching_program.html', levels=levels, level_databases=level_databases)

@main_bp.route('/notifications')
@login_required
def notifications():
    """
    عرض الإشعارات للمستخدم
    """
    # تحديد نوع الإشعارات حسب دور المستخدم
    if current_user.is_admin() or current_user.is_inspector():
        # الإشعارات بين الإدارة والمفتشين
        return render_template('notifications/admin_inspector_notifications.html')
    elif current_user.is_teacher():
        # الإشعارات بين المفتشين والأساتذة
        return render_template('notifications/inspector_teacher_notifications.html')
    else:
        flash('دور المستخدم غير معروف.', 'danger')
        return redirect(url_for('main.dashboard'))

@main_bp.route('/prepare_lesson')
@login_required
def prepare_lesson():
    """
    صفحة تحضير الدروس باستخدام الذكاء الاصطناعي
    """
    # هذه الصفحة متاحة للأساتذة والمفتشين فقط
    if current_user.is_teacher() or current_user.is_inspector():
        return render_template('prepare_lesson.html')
    else:
        flash('غير مصرح لك بالوصول إلى هذه الصفحة.', 'danger')
        return redirect(url_for('main.dashboard'))

@main_bp.route('/mark_notification_read/<notification_type>/<int:notification_id>')
@login_required
def mark_notification_read(notification_type, notification_id):
    """
    تحديد إشعار كمقروء
    """
    try:
        if notification_type == 'admin_inspector':
            # إشعار من الإدارة للمفتش
            notification = AdminInspectorNotification.query.get_or_404(notification_id)
            if notification.receiver_id != current_user.id:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'message': 'غير مصرح لك بالوصول إلى هذا الإشعار.'})
                flash('غير مصرح لك بالوصول إلى هذا الإشعار.', 'danger')
                return redirect(url_for('main.dashboard'))

            notification.is_read = True
            db.session.commit()

            # التحقق من نوع الطلب (AJAX أم عادي)
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء.'})

            flash('تم تحديد الإشعار كمقروء.', 'success')
            if current_user.is_inspector():
                return redirect(url_for('inspector.notifications'))
            else:
                return redirect(url_for('main.notifications'))

        elif notification_type == 'inspector_teacher':
            # إشعار من المفتش للأستاذ
            notification = InspectorTeacherNotification.query.get_or_404(notification_id)
            if notification.receiver_id != current_user.id:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'message': 'غير مصرح لك بالوصول إلى هذا الإشعار.'})
                flash('غير مصرح لك بالوصول إلى هذا الإشعار.', 'danger')
                return redirect(url_for('main.dashboard'))

            notification.is_read = True
            db.session.commit()

            # التحقق من نوع الطلب (AJAX أم عادي)
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء.'})

            flash('تم تحديد الإشعار كمقروء.', 'success')
            if current_user.is_teacher():
                return redirect(url_for('teacher.notifications'))
            else:
                return redirect(url_for('main.notifications'))
        else:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': 'نوع الإشعار غير صالح.'})
            flash('نوع الإشعار غير صالح.', 'danger')
            return redirect(url_for('main.dashboard'))

    except Exception:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحديث الإشعار.'})
        flash('حدث خطأ أثناء تحديث الإشعار.', 'danger')
        return redirect(url_for('main.dashboard'))
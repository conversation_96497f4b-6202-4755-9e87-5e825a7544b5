"""
تطبيق Ta9affi الجديد
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import LoginManager, login_user, login_required, logout_user, current_user
from flask_wtf.csrf import CSRFProtect
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_talisman import Talisman
from werkzeug.security import generate_password_hash, check_password_hash
import os
import pandas as pd
import secrets
from datetime import datetime, timezone

from models import db, User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, Schedule, ProgressEntry, LevelDatabase, LevelDataEntry, AdminInspectorNotification, InspectorTeacherNotification, inspector_teacher

# تهيئة تطبيق Flask
app = Flask(__name__)

# إعدادات الأمان
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ta9affi_new.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['NOTIFICATIONS_PER_PAGE'] = 10  # عدد الإشعارات في الصفحة الواحدة

# إعدادات CSRF
app.config['WTF_CSRF_ENABLED'] = True
app.config['WTF_CSRF_TIME_LIMIT'] = 3600  # ساعة واحدة

# تهيئة قاعدة البيانات
db.init_app(app)

# تهيئة حماية CSRF
csrf = CSRFProtect(app)

# تهيئة محدد معدل الطلبات
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["200 per day", "50 per hour"]
)

# تهيئة رؤوس الأمان
talisman = Talisman(
    app,
    force_https=False,  # تعطيل في التطوير
    content_security_policy={
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://code.jquery.com",
        'style-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com",
        'font-src': "'self' https://fonts.gstatic.com",
        'img-src': "'self' data:",
    }
)

# تهيئة مدير تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# إضافة فلتر nl2br لتنسيق النص في الرسائل
@app.template_filter('nl2br')
def nl2br(value):
    if value:
        return value.replace('\n', '<br>')
    return ''

# دالة للتحقق من قوة كلمة المرور
def is_password_strong(password):
    """
    التحقق من قوة كلمة المرور
    يجب أن تحتوي على:
    - 8 أحرف على الأقل
    - حرف كبير واحد على الأقل
    - حرف صغير واحد على الأقل
    - رقم واحد على الأقل
    """
    import re

    if len(password) < 8:
        return False, "كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل"

    if not re.search(r'[A-Z]', password):
        return False, "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل"

    if not re.search(r'[a-z]', password):
        return False, "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل"

    if not re.search(r'\d', password):
        return False, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"

    return True, "كلمة المرور قوية"

# دالة لتنظيف البيانات المدخلة
def sanitize_input(text):
    """تنظيف النص من الأحرف الخطيرة"""
    if not text:
        return text

    # إزالة الأحرف الخطيرة
    import html
    return html.escape(text.strip())

# دالة لتسجيل محاولات تسجيل الدخول
def log_security_event(event_type, username=None, ip_address=None, details=None):
    """تسجيل الأحداث الأمنية"""
    import logging

    # إعداد ملف السجل
    logging.basicConfig(
        filename='security.log',
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    message = f"{event_type}"
    if username:
        message += f" - Username: {username}"
    if ip_address:
        message += f" - IP: {ip_address}"
    if details:
        message += f" - Details: {details}"

    logging.info(message)

# تحميل المستخدم
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# معالج أخطاء CSRF
@app.errorhandler(400)
def csrf_error(reason):
    log_security_event(
        "CSRF_ERROR",
        ip_address=request.remote_addr,
        details=str(reason)
    )
    flash('خطأ في الأمان: طلب غير صالح', 'danger')
    return redirect(url_for('index'))

# معالج أخطاء محدد معدل الطلبات
@app.errorhandler(429)
def ratelimit_handler(e):
    log_security_event(
        "RATE_LIMIT_EXCEEDED",
        ip_address=request.remote_addr,
        details=str(e.description)
    )
    flash('تم تجاوز الحد الأقصى للطلبات. يرجى المحاولة لاحقاً', 'warning')
    return redirect(url_for('index'))

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
@limiter.limit("5 per minute")  # حد أقصى 5 محاولات تسجيل دخول في الدقيقة
def login():
    if request.method == 'POST':
        username = sanitize_input(request.form.get('username'))
        password = request.form.get('password')

        # التحقق من صحة البيانات
        if not username or not password:
            flash('اسم المستخدم وكلمة المرور مطلوبان', 'danger')
            return render_template('login.html')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password, password):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')

            # تسجيل تسجيل دخول ناجح
            log_security_event(
                "SUCCESSFUL_LOGIN",
                username=username,
                ip_address=request.remote_addr
            )

            # إعادة التوجيه إلى الصفحة المطلوبة أو لوحة التحكم
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('dashboard'))
        else:
            # تسجيل محاولة تسجيل دخول فاشلة
            log_security_event(
                "FAILED_LOGIN",
                username=username,
                ip_address=request.remote_addr,
                details="Invalid username or password"
            )
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')

    return render_template('login.html')

# تسجيل حساب جديد
@app.route('/register', methods=['GET', 'POST'])
@limiter.limit("3 per hour")  # حد أقصى 3 تسجيلات في الساعة
def register():
    if request.method == 'POST':
        username = sanitize_input(request.form.get('username'))
        email = sanitize_input(request.form.get('email'))
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        role = request.form.get('role')

        # التحقق من صحة البيانات
        if not username or not email or not password or not role:
            flash('جميع الحقول مطلوبة', 'danger')
            return render_template('register.html')

        # التحقق من تطابق كلمات المرور
        if password != confirm_password:
            flash('كلمات المرور غير متطابقة', 'danger')
            return render_template('register.html')

        # التحقق من قوة كلمة المرور
        is_strong, message = is_password_strong(password)
        if not is_strong:
            flash(message, 'danger')
            return render_template('register.html')

        # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return render_template('register.html')

        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'danger')
            return render_template('register.html')

        # إنشاء مستخدم جديد
        hashed_password = generate_password_hash(password)
        new_user = User(username=username, email=email, password=hashed_password, role=role)

        db.session.add(new_user)
        db.session.commit()

        flash('تم التسجيل بنجاح! يرجى تسجيل الدخول.', 'success')
        return redirect(url_for('login'))

    return render_template('register.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح!', 'success')
    return redirect(url_for('index'))

# الملف الشخصي
@app.route('/profile')
@login_required
def profile():
    return render_template('profile.html')

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    if current_user.role == Role.ADMIN:
        return redirect(url_for('admin_dashboard'))
    elif current_user.role == Role.INSPECTOR:
        return redirect(url_for('inspector_dashboard'))
    else:
        return redirect(url_for('teacher_dashboard'))

# لوحة تحكم الإدارة
@app.route('/dashboard/admin')
@login_required
def admin_dashboard():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()
    return render_template('admin_dashboard.html', inspectors=inspectors)

# إدارة قواعد البيانات المنفصلة
@app.route('/admin/databases')
@login_required
def manage_databases():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    levels = EducationalLevel.query.all()
    databases = LevelDatabase.query.all()

    return render_template('manage_level_databases.html', levels=levels, databases=databases)

# إضافة قاعدة بيانات جديدة
@app.route('/admin/databases/add', methods=['POST'])
@login_required
def add_database():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    level_id = request.form.get('level_id')
    name = request.form.get('name')
    file_path = request.form.get('file_path')
    is_active = 'is_active' in request.form

    # التحقق من وجود المستوى
    level = EducationalLevel.query.get(level_id)
    if not level:
        flash('المستوى التعليمي غير موجود', 'danger')
        return redirect(url_for('manage_databases'))

    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
    data_dir = os.path.join(app.root_path, 'data')
    os.makedirs(data_dir, exist_ok=True)

    # إنشاء قاعدة بيانات جديدة
    new_db = LevelDatabase(
        level_id=level_id,
        name=name,
        file_path=file_path,
        is_active=is_active
    )

    db.session.add(new_db)
    db.session.commit()

    flash('تم إضافة قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

# عرض قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/view')
@login_required
def view_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)
    entries = LevelDataEntry.query.filter_by(database_id=db_id).all()

    return render_template('view_database.html', database=database, entries=entries)

# تعديل قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if request.method == 'POST':
        database.name = request.form.get('name')
        database.file_path = request.form.get('file_path')
        database.is_active = 'is_active' in request.form

        db.session.commit()

        flash('تم تحديث قاعدة البيانات بنجاح', 'success')
        return redirect(url_for('manage_databases'))

    return render_template('edit_database.html', database=database)

# حذف قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/delete', methods=['POST'])
@login_required
def delete_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # حذف جميع العناصر أولاً
    LevelDataEntry.query.filter_by(database_id=db_id).delete()

    # حذف قاعدة البيانات
    db.session.delete(database)
    db.session.commit()

    flash('تم حذف قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

# تفعيل/تعطيل قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/toggle/<string:action>')
@login_required
def toggle_database(db_id, action):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if action == 'activate':
        database.is_active = True
        flash('تم تفعيل قاعدة البيانات بنجاح', 'success')
    elif action == 'deactivate':
        database.is_active = False
        flash('تم تعطيل قاعدة البيانات بنجاح', 'success')

    db.session.commit()
    return redirect(url_for('manage_databases'))

# إدارة عناصر قاعدة البيانات
@app.route('/admin/databases/<int:db_id>/entries/add', methods=['POST'])
@login_required
def add_database_entry(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    entry_type = request.form.get('entry_type')
    parent_id = request.form.get('parent_id')
    is_active = 'is_active' in request.form
    is_multiple = 'is_multiple' in request.form

    # التحقق من نوع الإضافة (مفردة أو متعددة)
    if is_multiple:
        # إضافة متعددة
        multiple_entries = request.form.get('multiple_entries', '')
        entries_list = [entry.strip() for entry in multiple_entries.split('\n') if entry.strip()]

        added_count = 0
        for entry_name in entries_list:
            # التحقق من وجود العنصر
            existing_entry = LevelDataEntry.query.filter_by(
                database_id=db_id,
                entry_type=entry_type,
                parent_id=parent_id if parent_id != '0' else None,
                name=entry_name
            ).first()

            if not existing_entry:
                # إنشاء عنصر جديد
                new_entry = LevelDataEntry(
                    database_id=db_id,
                    entry_type=entry_type,
                    parent_id=parent_id if parent_id != '0' else None,
                    name=entry_name,
                    description=f"مضاف بواسطة الإضافة المتعددة",
                    is_active=is_active
                )
                db.session.add(new_entry)
                added_count += 1

        db.session.commit()

        # إرجاع استجابة JSON للطلبات المتعددة
        return jsonify({
            'success': True,
            'message': f'تم إضافة {added_count} عنصر بنجاح',
            'count': added_count
        })
    else:
        # إضافة مفردة
        name = request.form.get('name')
        description = request.form.get('description')

        # إنشاء عنصر جديد
        new_entry = LevelDataEntry(
            database_id=db_id,
            entry_type=entry_type,
            parent_id=parent_id if parent_id != '0' else None,
            name=name,
            description=description,
            is_active=is_active
        )

        db.session.add(new_entry)
        db.session.commit()

        flash('تم إضافة العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/edit', methods=['POST'])
@login_required
def edit_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    entry.name = request.form.get('name')
    entry.description = request.form.get('description')
    entry.is_active = 'is_active' in request.form

    db.session.commit()

    flash('تم تحديث العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/delete', methods=['POST'])
@login_required
def delete_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    # حذف جميع العناصر الفرعية بشكل متكرر
    delete_child_entries(db_id, entry_id)

    # حذف العنصر
    db.session.delete(entry)
    db.session.commit()

    flash('تم حذف العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

# دالة مساعدة لحذف العناصر الفرعية بشكل متكرر
def delete_child_entries(db_id, parent_id):
    child_entries = LevelDataEntry.query.filter_by(database_id=db_id, parent_id=parent_id).all()

    for entry in child_entries:
        # حذف أبناء هذا العنصر
        delete_child_entries(db_id, entry.id)

        # حذف هذا العنصر
        db.session.delete(entry)

# لوحة تحكم المفتش
@app.route('/dashboard/inspector')
@login_required
def inspector_dashboard():
    try:
        # التحقق من المستخدم الحالي
        print(f"Current user: {current_user.username} (ID: {current_user.id}, Role: {current_user.role})")

        if current_user.role != Role.INSPECTOR:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

        # الحصول على الأساتذة الذين يشرف عليهم المفتش
        try:
            # محاولة استخدام العلاقة المباشرة
            teachers = current_user.supervised_teachers.all()
            print(f"Found {len(teachers)} supervised teachers for inspector {current_user.username} using relationship")

            # إذا لم يتم العثور على أي أستاذ، جرب استعلام SQL مباشر
            if not teachers:
                print("No teachers found using relationship, trying SQL query...")
                teachers_query = db.session.query(User).join(
                    inspector_teacher,
                    User.id == inspector_teacher.c.teacher_id
                ).filter(
                    inspector_teacher.c.inspector_id == current_user.id,
                    User.role == Role.TEACHER
                )
                teachers = teachers_query.all()
                print(f"Found {len(teachers)} supervised teachers for inspector {current_user.username} using SQL query")
        except Exception as e:
            print(f"Error fetching supervised teachers: {str(e)}")
            teachers = []

        # الحصول على الأساتذة المتاحين للإضافة
        try:
            supervised_teacher_ids = [t.id for t in teachers]
            # الحصول على جميع الأساتذة الذين ليسوا تحت إشراف المفتش الحالي
            available_teachers = User.query.filter_by(role=Role.TEACHER).all()
            if supervised_teacher_ids:
                available_teachers = [t for t in available_teachers if t.id not in supervised_teacher_ids]
            print(f"Found {len(available_teachers)} available teachers for inspector {current_user.username}")
        except Exception as e:
            print(f"Error fetching available teachers: {str(e)}")
            available_teachers = []

        # الحصول على بيانات التقدم لكل أستاذ
        teacher_progress = {}
        progress_stats = {
            'completed': 0,
            'in_progress': 0,
            'planned': 0,
            'total': 0
        }

        for teacher in teachers:
            try:
                # الحصول على سجلات التقدم للأستاذ
                entries = ProgressEntry.query.filter_by(user_id=teacher.id).all()
                print(f"Found {len(entries)} progress entries for teacher {teacher.username}")

                # حساب الإحصائيات
                stats = {
                    'completed': 0,
                    'in_progress': 0,
                    'planned': 0,
                    'total': len(entries)
                }

                for entry in entries:
                    if entry.status in stats:
                        stats[entry.status] += 1
                        progress_stats[entry.status] += 1

                print(f"Stats for teacher {teacher.username}: {stats}")

                progress_stats['total'] += stats['total']

                # حساب نسبة الإنجاز
                completion_rate = 0
                if stats['total'] > 0:
                    completion_rate = (stats['completed'] / stats['total']) * 100

                # الحصول على سجلات التقدم الأخيرة مع التفاصيل الكاملة
                recent_entries = ProgressEntry.query.filter_by(user_id=teacher.id).order_by(ProgressEntry.date.desc()).limit(5).all()
                detailed_entries = []
            except Exception as e:
                print(f"Error processing teacher {teacher.id}: {str(e)}")
                stats = {'completed': 0, 'in_progress': 0, 'planned': 0, 'total': 0}
                completion_rate = 0
                recent_entries = []
                detailed_entries = []

            for entry in recent_entries:
                entry_details = {
                    'id': entry.id,
                    'date': entry.date,
                    'status': entry.status,
                    'notes': entry.notes,
                    'level': None,
                    'subject': None,
                    'domain': None,
                    'material': None,
                    'competency': None
                }

                # الحصول على الكفاءة
                if entry.competency_id:
                    # محاولة الحصول على الكفاءة من نموذج LevelDataEntry
                    competency = LevelDataEntry.query.filter_by(id=entry.competency_id, entry_type='competency').first()
                    if competency:
                        entry_details['competency'] = {
                            'id': competency.id,
                            'name': competency.name,
                            'description': competency.description
                        }

                        # الحصول على المادة المعرفية (الأب)
                        if competency.parent_id:
                            material = LevelDataEntry.query.filter_by(id=competency.parent_id, entry_type='material').first()
                            if material:
                                entry_details['material'] = {
                                    'id': material.id,
                                    'name': material.name
                                }

                                # الحصول على الميدان (الجد)
                                if material.parent_id:
                                    domain = LevelDataEntry.query.filter_by(id=material.parent_id, entry_type='domain').first()
                                    if domain:
                                        entry_details['domain'] = {
                                            'id': domain.id,
                                            'name': domain.name
                                        }

                                        # الحصول على المادة الدراسية (الجد الأكبر)
                                        if domain.parent_id:
                                            subject = LevelDataEntry.query.filter_by(id=domain.parent_id, entry_type='subject').first()
                                            if subject:
                                                entry_details['subject'] = {
                                                    'id': subject.id,
                                                    'name': subject.name
                                                }

                                                # الحصول على المستوى التعليمي
                                                db = LevelDatabase.query.filter_by(id=subject.database_id).first()
                                                if db and db.level_id:
                                                    level = EducationalLevel.query.get(db.level_id)
                                                    if level:
                                                        entry_details['level'] = {
                                                            'id': level.id,
                                                            'name': level.name
                                                        }
                    else:
                        # محاولة الحصول على الكفاءة من نموذج Competency
                        competency = Competency.query.get(entry.competency_id)
                        if competency:
                            entry_details['competency'] = {
                                'id': competency.id,
                                'name': '',
                                'description': competency.description
                            }

                            # الحصول على المادة المعرفية
                            if competency.knowledge_material_id:
                                material = KnowledgeMaterial.query.get(competency.knowledge_material_id)
                                if material:
                                    entry_details['material'] = {
                                        'id': material.id,
                                        'name': material.name
                                    }

                                    # الحصول على الميدان
                                    if material.domain_id:
                                        domain = Domain.query.get(material.domain_id)
                                        if domain:
                                            entry_details['domain'] = {
                                                'id': domain.id,
                                                'name': domain.name
                                            }

                                            # الحصول على المادة الدراسية
                                            if domain.subject_id:
                                                subject = Subject.query.get(domain.subject_id)
                                                if subject:
                                                    entry_details['subject'] = {
                                                        'id': subject.id,
                                                        'name': subject.name
                                                    }

                                                    # الحصول على المستوى التعليمي
                                                    if subject.level_id:
                                                        level = EducationalLevel.query.get(subject.level_id)
                                                        if level:
                                                            entry_details['level'] = {
                                                                'id': level.id,
                                                                'name': level.name
                                                            }

                detailed_entries.append(entry_details)

            teacher_progress[teacher.id] = {
                'stats': stats,
                'completion_rate': completion_rate,
                'recent_entries': detailed_entries
            }
            print(f"Added progress data for teacher {teacher.username} with completion rate {completion_rate}%")

        # حساب نسبة الإنجاز الإجمالية
        overall_completion_rate = 0
        if progress_stats['total'] > 0:
            overall_completion_rate = (progress_stats['completed'] / progress_stats['total']) * 100

        # الحصول على المستويات التعليمية
        levels = EducationalLevel.query.all()
        level_stats = {}

        for level in levels:
            # الحصول على الجداول المرتبطة بهذا المستوى
            schedules = Schedule.query.filter_by(level_id=level.id).all()
            teacher_ids = [s.user_id for s in schedules if s.user_id in [t.id for t in teachers]]

            # الحصول على سجلات التقدم للأساتذة المرتبطين بهذا المستوى
            level_entries = ProgressEntry.query.filter(ProgressEntry.user_id.in_(teacher_ids)).all()

            # حساب الإحصائيات
            stats = {
                'completed': 0,
                'in_progress': 0,
                'planned': 0,
                'total': len(level_entries)
            }

            for entry in level_entries:
                if entry.status in stats:
                    stats[entry.status] += 1

            # حساب نسبة الإنجاز
            completion_rate = 0
            if stats['total'] > 0:
                completion_rate = (stats['completed'] / stats['total']) * 100

            level_stats[level.id] = {
                'name': level.name,
                'stats': stats,
                'completion_rate': completion_rate
            }

        # طباعة معلومات التصحيح قبل عرض القالب
        print(f"Final data for template:")
        print(f"- Teachers count: {len(teachers)}")
        print(f"- Available teachers count: {len(available_teachers)}")
        print(f"- Teacher progress keys: {list(teacher_progress.keys()) if teacher_progress else 'None'}")
        print(f"- Progress stats: {progress_stats}")
        print(f"- Overall completion rate: {overall_completion_rate}%")
        print(f"- Level stats keys: {list(level_stats.keys()) if level_stats else 'None'}")

        return render_template('inspector_dashboard.html',
                               teachers=teachers,
                               available_teachers=available_teachers,
                               teacher_progress=teacher_progress,
                               progress_stats=progress_stats,
                               overall_completion_rate=overall_completion_rate,
                               level_stats=level_stats)

    except Exception as e:
        print(f"Error in inspector_dashboard: {str(e)}")
        flash('حدث خطأ أثناء تحميل لوحة التحكم', 'danger')
        # تمرير قيم افتراضية لجميع المتغيرات المطلوبة
        return render_template('inspector_dashboard.html',
                               teachers=[],
                               available_teachers=[],
                               teacher_progress={},
                               progress_stats={'completed': 0, 'in_progress': 0, 'planned': 0, 'total': 0},
                               overall_completion_rate=0,
                               level_stats={})

# لوحة تحكم الأستاذ
@app.route('/dashboard/teacher')
@login_required
def teacher_dashboard():
    try:
        if current_user.role != Role.TEACHER:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

        # الحصول على جدول الأستاذ وتقدمه
        try:
            schedules = Schedule.query.filter_by(user_id=current_user.id).all()
        except Exception as e:
            print(f"Error fetching schedules: {str(e)}")
            schedules = []

        # الحصول على آخر تحديثات التقدم
        try:
            progress_entries_raw = ProgressEntry.query.filter_by(user_id=current_user.id).order_by(ProgressEntry.date.desc()).all()
        except Exception as e:
            print(f"Error fetching progress entries: {str(e)}")
            progress_entries_raw = []

        # التحقق من وجود الكفاءات المرتبطة
        detailed_entries = []

        # إحصائيات التقدم
        progress_stats = {
            'completed': 0,
            'in_progress': 0,
            'planned': 0,
            'total': 0
        }

        for entry in progress_entries_raw:
            try:
                entry_details = {
                    'id': entry.id,
                    'date': entry.date,
                    'status': entry.status,
                    'notes': entry.notes,
                    'level': None,
                    'subject': None,
                    'domain': None,
                    'material': None,
                    'competency': None
                }

                # التحقق من وجود الكفاءة
                if entry.competency_id:
                    # محاولة الحصول على الكفاءة من نموذج LevelDataEntry
                    competency = LevelDataEntry.query.filter_by(id=entry.competency_id, entry_type='competency').first()
                    if competency:
                        entry_details['competency'] = {
                            'id': competency.id,
                            'name': competency.name,
                            'description': competency.description
                        }

                        # تحديث الإحصائيات
                        if entry.status in progress_stats:
                            progress_stats[entry.status] += 1
                        progress_stats['total'] += 1

                        # الحصول على المادة المعرفية (الأب)
                        if competency.parent_id:
                            material = LevelDataEntry.query.filter_by(id=competency.parent_id, entry_type='material').first()
                            if material:
                                entry_details['material'] = {
                                    'id': material.id,
                                    'name': material.name
                                }

                                # الحصول على الميدان (الجد)
                                if material.parent_id:
                                    domain = LevelDataEntry.query.filter_by(id=material.parent_id, entry_type='domain').first()
                                    if domain:
                                        entry_details['domain'] = {
                                            'id': domain.id,
                                            'name': domain.name
                                        }

                                        # الحصول على المادة الدراسية (الجد الأكبر)
                                        if domain.parent_id:
                                            subject = LevelDataEntry.query.filter_by(id=domain.parent_id, entry_type='subject').first()
                                            if subject:
                                                entry_details['subject'] = {
                                                    'id': subject.id,
                                                    'name': subject.name
                                                }

                                                # الحصول على المستوى التعليمي
                                                db = LevelDatabase.query.filter_by(id=subject.database_id).first()
                                                if db and db.level_id:
                                                    level = EducationalLevel.query.get(db.level_id)
                                                    if level:
                                                        entry_details['level'] = {
                                                            'id': level.id,
                                                            'name': level.name
                                                        }
                    else:
                        # محاولة الحصول على الكفاءة من نموذج Competency
                        competency = Competency.query.get(entry.competency_id)
                        if competency:
                            entry_details['competency'] = {
                                'id': competency.id,
                                'name': '',
                                'description': competency.description
                            }

                            # تحديث الإحصائيات
                            if entry.status in progress_stats:
                                progress_stats[entry.status] += 1
                            progress_stats['total'] += 1

                            # الحصول على المادة المعرفية
                            if competency.knowledge_material_id:
                                material = KnowledgeMaterial.query.get(competency.knowledge_material_id)
                                if material:
                                    entry_details['material'] = {
                                        'id': material.id,
                                        'name': material.name
                                    }

                                    # الحصول على الميدان
                                    if material.domain_id:
                                        domain = Domain.query.get(material.domain_id)
                                        if domain:
                                            entry_details['domain'] = {
                                                'id': domain.id,
                                                'name': domain.name
                                            }

                                            # الحصول على المادة الدراسية
                                            if domain.subject_id:
                                                subject = Subject.query.get(domain.subject_id)
                                                if subject:
                                                    entry_details['subject'] = {
                                                        'id': subject.id,
                                                        'name': subject.name
                                                    }

                                                    # الحصول على المستوى التعليمي
                                                    if subject.level_id:
                                                        level = EducationalLevel.query.get(subject.level_id)
                                                        if level:
                                                            entry_details['level'] = {
                                                                'id': level.id,
                                                                'name': level.name
                                                            }

                detailed_entries.append(entry_details)
            except Exception as e:
                print(f"Error processing entry {entry.id}: {str(e)}")
                continue

        # حساب نسبة الإنجاز
        completion_rate = 0
        if progress_stats['total'] > 0:
            completion_rate = (progress_stats['completed'] / progress_stats['total']) * 100

        # الحصول على إحصائيات حسب المستوى والمادة
        level_stats = {}
        subject_stats = {}

        # الحصول على جميع الجداول الخاصة بالأستاذ
        for schedule in schedules:
            # إضافة المستوى إلى الإحصائيات
            if schedule.level_id not in level_stats and schedule.level:
                level_stats[schedule.level_id] = {
                    'name': schedule.level.name,
                    'completed': 0,
                    'in_progress': 0,
                    'planned': 0,
                    'total': 0
                }

            # إضافة المادة إلى الإحصائيات
            if schedule.subject_id not in subject_stats and schedule.subject:
                subject_stats[schedule.subject_id] = {
                    'name': schedule.subject.name,
                    'completed': 0,
                    'in_progress': 0,
                    'planned': 0,
                    'total': 0
                }

        # تحديث إحصائيات المستوى والمادة بناءً على سجلات التقدم
        for entry_details in detailed_entries:
            try:
                # الحصول على المستوى والمادة من التفاصيل
                if entry_details['level'] and entry_details['level']['id'] in level_stats:
                    level_stats[entry_details['level']['id']][entry_details['status']] += 1
                    level_stats[entry_details['level']['id']]['total'] += 1

                if entry_details['subject'] and entry_details['subject']['id'] in subject_stats:
                    subject_stats[entry_details['subject']['id']][entry_details['status']] += 1
                    subject_stats[entry_details['subject']['id']]['total'] += 1
            except Exception as e:
                print(f"Error updating stats for entry {entry_details['id']}: {str(e)}")
                continue

        # حساب نسب الإنجاز لكل مستوى ومادة
        for level_id in level_stats:
            if level_stats[level_id]['total'] > 0:
                level_stats[level_id]['completion_rate'] = (level_stats[level_id]['completed'] / level_stats[level_id]['total']) * 100
            else:
                level_stats[level_id]['completion_rate'] = 0

        for subject_id in subject_stats:
            if subject_stats[subject_id]['total'] > 0:
                subject_stats[subject_id]['completion_rate'] = (subject_stats[subject_id]['completed'] / subject_stats[subject_id]['total']) * 100
            else:
                subject_stats[subject_id]['completion_rate'] = 0

        # الحصول على آخر الإشعارات
        try:
            notifications = InspectorTeacherNotification.query.filter_by(receiver_id=current_user.id).order_by(InspectorTeacherNotification.created_at.desc()).limit(5).all()
        except Exception as e:
            print(f"Error fetching notifications: {str(e)}")
            notifications = []

        # الحصول على جميع المستويات والمواد المتاحة في النظام
        all_levels = EducationalLevel.query.all()
        all_subjects = LevelDataEntry.query.filter_by(entry_type='subject').all()

        # إنشاء قواميس لجميع المستويات والمواد
        all_level_stats = {}
        for level in all_levels:
            all_level_stats[level.id] = {
                'name': level.name,
                'completed': 0,
                'in_progress': 0,
                'planned': 0,
                'total': 0,
                'completion_rate': 0
            }

        all_subject_stats = {}
        for subject in all_subjects:
            # الحصول على معرف المستوى من قاعدة البيانات
            level_id = None
            if subject.database_id:
                db = LevelDatabase.query.get(subject.database_id)
                if db and db.level_id:
                    level_id = db.level_id

            all_subject_stats[subject.id] = {
                'name': subject.name,
                'level_id': level_id,
                'completed': 0,
                'in_progress': 0,
                'planned': 0,
                'total': 0,
                'completion_rate': 0
            }

        # دمج الإحصائيات الحالية مع الإحصائيات الكاملة
        for level_id, stats in level_stats.items():
            if level_id in all_level_stats:
                all_level_stats[level_id].update(stats)

        for subject_id, stats in subject_stats.items():
            if subject_id in all_subject_stats:
                all_subject_stats[subject_id].update(stats)

        # التأكد من وجود المتغيرات المطلوبة
        context = {
            'schedules': schedules or [],
            'progress_entries': detailed_entries or [],
            'progress_stats': progress_stats,
            'completion_rate': completion_rate,
            'level_stats': all_level_stats,
            'subject_stats': all_subject_stats,
            'notifications': notifications
        }

        return render_template('teacher_dashboard.html', **context)

    except Exception as e:
        print(f"Error in teacher_dashboard: {str(e)}")
        flash('حدث خطأ أثناء تحميل لوحة التحكم', 'danger')
        return render_template('teacher_dashboard.html', schedules=[], progress_entries=[])

# البرنامج السنوي للتدريس
@app.route('/teaching-program')
@login_required
def teaching_program():
    levels = EducationalLevel.query.all()

    # الحصول على قواعد البيانات النشطة لكل مستوى
    level_databases = {}
    for level in levels:
        db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
        if db:
            level_databases[level.id] = db.id

    return render_template('teaching_program.html', levels=levels, level_databases=level_databases)

@app.route('/admin/cleanup-inactive-levels', methods=['GET'])
@login_required
def cleanup_inactive_levels():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # الحصول على جميع المستويات التعليمية
        all_levels = EducationalLevel.query.all()
        deleted_count = 0

        for level in all_levels:
            # التحقق من وجود قاعدة بيانات نشطة لهذا المستوى
            active_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()

            if not active_db:
                # حذف المستوى إذا لم يكن لديه قاعدة بيانات نشطة
                db.session.delete(level)
                deleted_count += 1

        # حفظ التغييرات
        db.session.commit()

        if deleted_count > 0:
            flash(f'تم حذف {deleted_count} مستوى تعليمي ليس لديه قاعدة بيانات نشطة', 'success')
        else:
            flash('لم يتم العثور على مستويات تعليمية ليس لديها قواعد بيانات نشطة', 'info')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المستويات: {str(e)}', 'danger')

    return redirect(url_for('teaching_program'))

# مسارات API للقوائم المنسدلة المعتمدة
@app.route('/api/subjects/<int:level_id>')
def get_subjects(level_id):
    result_subjects = []

    # 1. الحصول على المواد من جدول Subject
    standard_subjects = Subject.query.filter_by(level_id=level_id).all()
    for subject in standard_subjects:
        result_subjects.append({
            'id': subject.id,
            'name': subject.name,
            'source': 'subject'
        })

    # 2. الحصول على المواد من قاعدة البيانات الخاصة بالمستوى
    level_db = LevelDatabase.query.filter_by(level_id=level_id, is_active=True).first()
    if level_db:
        db_subjects = LevelDataEntry.query.filter_by(
            database_id=level_db.id,
            entry_type='subject',
            is_active=True
        ).all()

        # إضافة المواد من قاعدة البيانات
        existing_names = {subject['name'] for subject in result_subjects}
        for subject in db_subjects:
            # تجنب التكرار إذا كانت المادة موجودة بالفعل في جدول Subject
            if subject.name not in existing_names:
                result_subjects.append({
                    'id': subject.id,
                    'name': subject.name,
                    'source': 'database'
                })
                existing_names.add(subject.name)

    # ترتيب النتائج حسب الاسم
    result_subjects.sort(key=lambda x: x['name'])

    return jsonify(result_subjects)

@app.route('/api/domains/<int:subject_id>')
def get_domains(subject_id):
    # الحصول على المادة الدراسية
    subject = LevelDataEntry.query.get(subject_id)
    if not subject:
        return jsonify([])

    # الحصول على الميادين المرتبطة بالمادة الدراسية
    domains = LevelDataEntry.query.filter_by(database_id=subject.database_id, entry_type='domain', parent_id=subject_id, is_active=True).all()

    # إزالة التكرارات
    unique_domains = []
    seen_ids = set()

    for domain in domains:
        if domain.id not in seen_ids:
            seen_ids.add(domain.id)
            unique_domains.append(domain)

    return jsonify([{'id': d.id, 'name': d.name} for d in unique_domains])

@app.route('/api/knowledge-materials/<int:domain_id>')
def get_knowledge_materials(domain_id):
    # الحصول على الميدان
    domain = LevelDataEntry.query.get(domain_id)
    if not domain:
        return jsonify([])

    # الحصول على المواد المعرفية المرتبطة بالميدان
    materials = LevelDataEntry.query.filter_by(database_id=domain.database_id, entry_type='material', parent_id=domain_id, is_active=True).all()

    # إزالة التكرارات
    unique_materials = []
    seen_ids = set()

    for material in materials:
        if material.id not in seen_ids:
            seen_ids.add(material.id)
            unique_materials.append(material)

    return jsonify([{'id': m.id, 'name': m.name} for m in unique_materials])

@app.route('/api/competencies/<int:material_id>')
def get_competencies(material_id):
    # الحصول على المادة المعرفية
    material = LevelDataEntry.query.get(material_id)
    if not material:
        return jsonify([])

    # الحصول على الكفاءات المرتبطة بالمادة المعرفية
    competencies = LevelDataEntry.query.filter_by(database_id=material.database_id, entry_type='competency', parent_id=material_id, is_active=True).all()

    # إزالة التكرارات
    unique_competencies = []
    seen_ids = set()

    for competency in competencies:
        if competency.id not in seen_ids:
            seen_ids.add(competency.id)
            unique_competencies.append(competency)

    return jsonify([{
        'id': c.id,
        'name': c.name or '',
        'description': c.description or ''
    } for c in unique_competencies])

# تسجيل التقدم
@app.route('/progress/add', methods=['POST'])
@login_required
def add_progress():
    competency_id = request.form.get('competency_id')
    date_str = request.form.get('date')
    status = request.form.get('status')
    notes = request.form.get('notes')

    # التحقق من وجود الكفاءة
    competency = LevelDataEntry.query.get(competency_id)
    if not competency or competency.entry_type != 'competency':
        flash('الكفاءة غير موجودة', 'danger')
        return redirect(url_for('teaching_program'))

    # تحويل التاريخ من نص إلى كائن date
    try:
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        flash('تنسيق التاريخ غير صحيح', 'danger')
        return redirect(url_for('teaching_program'))

    # التحقق من الحالة
    if status not in ['completed', 'in_progress', 'planned']:
        status = 'planned'

    # إنشاء سجل تقدم جديد
    new_progress = ProgressEntry(
        user_id=current_user.id,
        competency_id=competency_id,
        date=date,
        status=status,
        notes=notes
    )

    db.session.add(new_progress)
    db.session.commit()

    flash('تم إضافة التقدم بنجاح', 'success')
    return redirect(url_for('teaching_program'))

# إدارة جدول التدريس
@app.route('/schedule/manage')
@login_required
def manage_schedule():
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    schedules = Schedule.query.filter_by(user_id=current_user.id).all()
    levels = EducationalLevel.query.all()

    return render_template('manage_schedule.html', schedules=schedules, levels=levels)

@app.route('/schedule/add', methods=['POST'])
@login_required
def add_schedule():
    day_of_week = int(request.form.get('day_of_week'))
    start_time_str = request.form.get('start_time')
    end_time_str = request.form.get('end_time')
    subject_id = request.form.get('subject_id')
    level_id = request.form.get('level_id')

    # تحويل الوقت من نص إلى كائن time
    start_time = datetime.strptime(start_time_str, '%H:%M').time()
    end_time = datetime.strptime(end_time_str, '%H:%M').time()

    # التحقق من وجود المادة الدراسية
    subject = None

    # محاولة الحصول على المادة من جدول Subject
    subject = Subject.query.filter_by(id=subject_id).first()

    # إذا لم يتم العثور على المادة، نحاول البحث في قاعدة البيانات الخاصة بالمستوى
    if not subject:
        # الحصول على قاعدة البيانات النشطة للمستوى
        level_db = LevelDatabase.query.filter_by(level_id=level_id, is_active=True).first()
        if level_db:
            # الحصول على المادة من قاعدة البيانات
            entry = LevelDataEntry.query.filter_by(id=subject_id, database_id=level_db.id, entry_type='subject').first()
            if entry:
                # البحث عن مادة موجودة بنفس الاسم في جدول Subject
                subject = Subject.query.filter_by(name=entry.name, level_id=level_id).first()

                # إنشاء مادة جديدة إذا لم تكن موجودة
                if not subject:
                    subject = Subject(name=entry.name, level_id=level_id)
                    db.session.add(subject)
                    db.session.flush()  # للحصول على معرف المادة الجديدة

    if not subject:
        flash('لم يتم العثور على المادة الدراسية', 'danger')
        return redirect(url_for('manage_schedule'))

    new_schedule = Schedule(
        user_id=current_user.id,
        day_of_week=day_of_week,
        start_time=start_time,
        end_time=end_time,
        subject_id=subject.id,
        level_id=level_id
    )

    db.session.add(new_schedule)
    db.session.commit()

    flash('تم إضافة الحصة بنجاح', 'success')
    return redirect(url_for('manage_schedule'))

@app.route('/schedule/delete/<int:schedule_id>', methods=['POST'])
@login_required
def delete_schedule(schedule_id):
    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الجدول ينتمي للمستخدم الحالي
    if schedule.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('manage_schedule'))

    db.session.delete(schedule)
    db.session.commit()

    flash('تم حذف الحصة بنجاح', 'success')
    return redirect(url_for('manage_schedule'))

@app.route('/api/schedule/<int:schedule_id>')
@login_required
def get_schedule(schedule_id):
    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الجدول ينتمي للمستخدم الحالي
    if schedule.user_id != current_user.id:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    # تحويل الوقت إلى تنسيق مناسب لحقل الإدخال من نوع time
    start_time = schedule.start_time.strftime('%H:%M')
    end_time = schedule.end_time.strftime('%H:%M')

    return jsonify({
        'id': schedule.id,
        'day_of_week': schedule.day_of_week,
        'start_time': start_time,
        'end_time': end_time,
        'subject_id': schedule.subject_id,
        'level_id': schedule.level_id
    })

@app.route('/schedule/edit/<int:schedule_id>', methods=['POST'])
@login_required
def edit_schedule(schedule_id):
    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الجدول ينتمي للمستخدم الحالي
    if schedule.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('manage_schedule'))

    # الحصول على البيانات من النموذج
    day_of_week = int(request.form.get('day_of_week'))
    start_time_str = request.form.get('start_time')
    end_time_str = request.form.get('end_time')
    subject_id = request.form.get('subject_id')
    level_id = request.form.get('level_id')

    # تحويل الوقت من نص إلى كائن time
    start_time = datetime.strptime(start_time_str, '%H:%M').time()
    end_time = datetime.strptime(end_time_str, '%H:%M').time()

    # التحقق من وجود المادة الدراسية
    subject = None

    # محاولة الحصول على المادة من جدول Subject
    subject = Subject.query.filter_by(id=subject_id).first()

    # إذا لم يتم العثور على المادة، نحاول البحث في قاعدة البيانات الخاصة بالمستوى
    if not subject:
        # الحصول على قاعدة البيانات النشطة للمستوى
        level_db = LevelDatabase.query.filter_by(level_id=level_id, is_active=True).first()
        if level_db:
            # الحصول على المادة من قاعدة البيانات
            entry = LevelDataEntry.query.filter_by(id=subject_id, database_id=level_db.id, entry_type='subject').first()
            if entry:
                # البحث عن مادة موجودة بنفس الاسم في جدول Subject
                subject = Subject.query.filter_by(name=entry.name, level_id=level_id).first()

                # إنشاء مادة جديدة إذا لم تكن موجودة
                if not subject:
                    subject = Subject(name=entry.name, level_id=level_id)
                    db.session.add(subject)
                    db.session.flush()  # للحصول على معرف المادة الجديدة

    if not subject:
        flash('لم يتم العثور على المادة الدراسية', 'danger')
        return redirect(url_for('manage_schedule'))

    # تحديث بيانات الحصة
    schedule.day_of_week = day_of_week
    schedule.start_time = start_time
    schedule.end_time = end_time
    schedule.subject_id = subject.id
    schedule.level_id = level_id

    db.session.commit()

    flash('تم تحديث الحصة بنجاح', 'success')
    return redirect(url_for('manage_schedule'))

# تصدير واستيراد البيانات
@app.route('/data/export/<string:model_name>')
@login_required
def export_data(model_name):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    model_map = {
        'levels': EducationalLevel,
        'subjects': Subject,
        'domains': Domain,
        'materials': KnowledgeMaterial,
        'competencies': Competency
    }

    if model_name not in model_map:
        flash('اسم النموذج غير صحيح', 'danger')
        return redirect(url_for('admin_dashboard'))

    model = model_map[model_name]
    data = model.query.all()

    # تحويل إلى DataFrame
    df_data = []
    for item in data:
        item_dict = {column.name: getattr(item, column.name) for column in item.__table__.columns}
        df_data.append(item_dict)

    df = pd.DataFrame(df_data)

    # حفظ إلى Excel
    filename = f"{model_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    df.to_excel(filepath, index=False)

    return redirect(url_for('static', filename=f'exports/{filename}'))

@app.route('/data/import/<string:model_name>', methods=['POST'])
@login_required
def import_data(model_name):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    model_map = {
        'levels': EducationalLevel,
        'subjects': Subject,
        'domains': Domain,
        'materials': KnowledgeMaterial,
        'competencies': Competency
    }

    if model_name not in model_map:
        flash('اسم النموذج غير صحيح', 'danger')
        return redirect(url_for('admin_dashboard'))

    if 'file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('admin_dashboard'))

    file = request.files['file']

    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('admin_dashboard'))

    if not file.filename.endswith('.xlsx'):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx)', 'danger')
        return redirect(url_for('admin_dashboard'))

    # قراءة ملف Excel
    df = pd.read_excel(file)

    # استيراد البيانات
    model = model_map[model_name]
    for _, row in df.iterrows():
        # تحويل الصف إلى قاموس
        row_dict = row.to_dict()

        # إزالة عمود id إذا كان موجوداً
        if 'id' in row_dict:
            del row_dict['id']

        # إنشاء نموذج جديد
        new_instance = model(**row_dict)
        db.session.add(new_instance)

    db.session.commit()

    flash(f'تم استيراد البيانات بنجاح لـ {model_name}', 'success')
    return redirect(url_for('admin_dashboard'))

# تصدير جميع قواعد البيانات
@app.route('/admin/databases/export-all')
@login_required
def export_all_databases():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # إنشاء كاتب لملف Excel
    filename = f"all_databases_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    # التحقق من وجود بيانات للتصدير
    levels = EducationalLevel.query.all()
    databases = LevelDatabase.query.all()

    if not levels and not databases:
        flash('لا توجد بيانات للتصدير', 'warning')
        return redirect(url_for('manage_databases'))

    # إنشاء كاتب Excel
    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    has_data = False

    # تصدير المستويات
    if levels:
        levels_data = [{
            'id': level.id,
            'name': level.name,
            'is_active': level.is_active,
            'database_prefix': level.database_prefix
        } for level in levels]

        pd.DataFrame(levels_data).to_excel(writer, sheet_name='Levels', index=False)
        has_data = True

    # تصدير قواعد البيانات
    if databases:
        databases_data = [{
            'id': db.id,
            'level_id': db.level_id,
            'level_name': db.level.name if db.level else '',
            'name': db.name,
            'file_path': db.file_path,
            'is_active': db.is_active,
            'created_at': db.created_at.strftime('%Y-%m-%d %H:%M:%S')
        } for db in databases]

        pd.DataFrame(databases_data).to_excel(writer, sheet_name='Databases', index=False)
        has_data = True

    # تصدير بيانات كل قاعدة بيانات
    for db in databases:
        if db.is_active:
            # تصدير المواد الدراسية
            subjects = LevelDataEntry.query.filter_by(database_id=db.id, entry_type='subject').all()
            if subjects:
                subjects_data = [{
                    'id': s.id,
                    'database_id': s.database_id,
                    'database_name': db.name,
                    'name': s.name,
                    'description': s.description,
                    'is_active': s.is_active
                } for s in subjects]

                sheet_name = f"Subjects_{db.id}"
                # التأكد من أن اسم الورقة لا يتجاوز 31 حرفًا (حد Excel)
                if len(sheet_name) > 31:
                    sheet_name = sheet_name[:31]
                pd.DataFrame(subjects_data).to_excel(writer, sheet_name=sheet_name, index=False)
                has_data = True

    # إذا لم تكن هناك أي بيانات للتصدير، أضف ورقة فارغة
    if not has_data:
        pd.DataFrame([{'info': 'لا توجد بيانات'}]).to_excel(writer, sheet_name='Info', index=False)

    writer.close()

    return redirect(url_for('static', filename=f'exports/{filename}'))

# تصدير نوع محدد من بيانات قاعدة البيانات
@app.route('/admin/databases/<int:db_id>/export/<string:entry_type>')
@login_required
def export_database_entry_type(db_id, entry_type):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # التحقق من نوع البيانات
    valid_types = ['subject', 'domain', 'material', 'competency']
    if entry_type not in valid_types:
        flash('نوع البيانات غير صحيح', 'danger')
        return redirect(url_for('view_database', db_id=db_id))

    # إنشاء كاتب لملف Excel
    type_name = {
        'subject': 'المواد_الدراسية',
        'domain': 'الميادين',
        'material': 'المواد_المعرفية',
        'competency': 'الكفاءات'
    }

    filename = f"{database.name}_{type_name[entry_type]}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    writer = pd.ExcelWriter(filepath, engine='openpyxl')

    # تصدير معلومات قاعدة البيانات
    db_info = [{
        'id': database.id,
        'name': database.name,
        'level': database.level.name if database.level else '',
        'level_id': database.level_id,
        'file_path': database.file_path,
        'is_active': database.is_active,
        'created_at': database.created_at.strftime('%Y-%m-%d %H:%M:%S')
    }]
    pd.DataFrame(db_info).to_excel(writer, sheet_name='Database_Info', index=False)

    # تصدير البيانات حسب النوع
    entries = LevelDataEntry.query.filter_by(database_id=db_id, entry_type=entry_type).all()

    if entries:
        if entry_type == 'subject':
            entries_data = [{
                'id': e.id,
                'name': e.name,
                'description': e.description,
                'is_active': e.is_active,
                'database_id': db_id,
                'level_id': database.level_id,
                'level_name': database.level.name if database.level else ''
            } for e in entries]
        elif entry_type == 'domain':
            entries_data = []
            for e in entries:
                parent = LevelDataEntry.query.get(e.parent_id) if e.parent_id else None
                entries_data.append({
                    'id': e.id,
                    'name': e.name,
                    'description': e.description,
                    'parent_id': e.parent_id,
                    'parent_name': parent.name if parent else '',
                    'subject_id': e.parent_id,  # معرف المادة الدراسية
                    'subject_name': parent.name if parent else '',
                    'database_id': db_id,
                    'level_id': database.level_id,
                    'level_name': database.level.name if database.level else '',
                    'is_active': e.is_active
                })
        elif entry_type == 'material':
            entries_data = []
            for e in entries:
                parent = LevelDataEntry.query.get(e.parent_id) if e.parent_id else None
                subject_id = None
                subject_name = ''

                # الحصول على المادة الدراسية المرتبطة بالميدان
                if parent and parent.parent_id:
                    subject = LevelDataEntry.query.get(parent.parent_id)
                    if subject:
                        subject_id = subject.id
                        subject_name = subject.name

                entries_data.append({
                    'id': e.id,
                    'name': e.name,
                    'description': e.description,
                    'parent_id': e.parent_id,  # معرف الميدان
                    'parent_name': parent.name if parent else '',  # اسم الميدان
                    'domain_id': e.parent_id,
                    'domain_name': parent.name if parent else '',
                    'subject_id': subject_id,
                    'subject_name': subject_name,
                    'database_id': db_id,
                    'level_id': database.level_id,
                    'level_name': database.level.name if database.level else '',
                    'is_active': e.is_active
                })
        elif entry_type == 'competency':
            entries_data = []
            for e in entries:
                parent = LevelDataEntry.query.get(e.parent_id) if e.parent_id else None
                domain_id = None
                domain_name = ''
                subject_id = None
                subject_name = ''

                # الحصول على الميدان والمادة الدراسية المرتبطة بالمادة المعرفية
                if parent and parent.parent_id:
                    domain = LevelDataEntry.query.get(parent.parent_id)
                    if domain:
                        domain_id = domain.id
                        domain_name = domain.name
                        if domain.parent_id:
                            subject = LevelDataEntry.query.get(domain.parent_id)
                            if subject:
                                subject_id = subject.id
                                subject_name = subject.name

                entries_data.append({
                    'id': e.id,
                    'name': e.name,
                    'description': e.description,
                    'parent_id': e.parent_id,  # معرف المادة المعرفية
                    'parent_name': parent.name if parent else '',  # اسم المادة المعرفية
                    'material_id': e.parent_id,
                    'material_name': parent.name if parent else '',
                    'domain_id': domain_id,
                    'domain_name': domain_name,
                    'subject_id': subject_id,
                    'subject_name': subject_name,
                    'database_id': db_id,
                    'level_id': database.level_id,
                    'level_name': database.level.name if database.level else '',
                    'is_active': e.is_active
                })

        # إضافة ورقة للبيانات المترابطة
        if entry_type == 'competency':
            hierarchical_data = []
            for e in entries:
                parent_material = LevelDataEntry.query.get(e.parent_id) if e.parent_id else None
                domain_id = None
                domain_name = ''
                subject_id = None
                subject_name = ''

                if parent_material and parent_material.parent_id:
                    domain = LevelDataEntry.query.get(parent_material.parent_id)
                    if domain:
                        domain_id = domain.id
                        domain_name = domain.name
                        if domain.parent_id:
                            subject = LevelDataEntry.query.get(domain.parent_id)
                            if subject:
                                subject_id = subject.id
                                subject_name = subject.name

                hierarchical_data.append({
                    'level_id': database.level_id,
                    'level_name': database.level.name if database.level else '',
                    'subject_id': subject_id,
                    'subject_name': subject_name,
                    'domain_id': domain_id,
                    'domain_name': domain_name,
                    'material_id': e.parent_id,
                    'material_name': parent_material.name if parent_material else '',
                    'competency_id': e.id,
                    'competency_name': e.name,
                    'competency_description': e.description,
                    'is_active': e.is_active
                })

            if hierarchical_data:
                pd.DataFrame(hierarchical_data).to_excel(writer, sheet_name='Hierarchical_Data', index=False)

        sheet_name = type_name[entry_type]
        if len(sheet_name) > 31:
            sheet_name = sheet_name[:31]
        pd.DataFrame(entries_data).to_excel(writer, sheet_name=sheet_name, index=False)
    else:
        pd.DataFrame([{'info': f'لا توجد بيانات من نوع {type_name[entry_type]}'}]).to_excel(writer, sheet_name='Info', index=False)

    writer.close()

    return redirect(url_for('static', filename=f'exports/{filename}'))

# تصدير واستيراد بيانات قاعدة البيانات
@app.route('/admin/databases/<int:db_id>/export')
@login_required
def export_database_data(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # إنشاء كاتب لملف Excel
    filename = f"database_{database.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    has_data = False

    # تصدير معلومات قاعدة البيانات
    db_info = [{
        'id': database.id,
        'name': database.name,
        'level': database.level.name if database.level else '',
        'level_id': database.level_id,
        'file_path': database.file_path,
        'is_active': database.is_active,
        'created_at': database.created_at.strftime('%Y-%m-%d %H:%M:%S')
    }]
    pd.DataFrame(db_info).to_excel(writer, sheet_name='Database_Info', index=False)
    has_data = True

    # تصدير المواد الدراسية
    subjects = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='subject').all()
    subjects_data = [{
        'id': s.id,
        'name': s.name,
        'description': s.description,
        'is_active': s.is_active,
        'database_id': db_id,
        'level_id': database.level_id,
        'level_name': database.level.name if database.level else ''
    } for s in subjects]

    if subjects_data:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='Subjects', index=False)
        has_data = True

    # تصدير الميادين
    domains = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='domain').all()
    domains_data = []

    for d in domains:
        parent = LevelDataEntry.query.get(d.parent_id) if d.parent_id else None
        domains_data.append({
            'id': d.id,
            'name': d.name,
            'description': d.description,
            'parent_id': d.parent_id,
            'parent_name': parent.name if parent else '',
            'subject_id': d.parent_id,  # معرف المادة الدراسية
            'subject_name': parent.name if parent else '',
            'database_id': db_id,
            'level_id': database.level_id,
            'level_name': database.level.name if database.level else '',
            'is_active': d.is_active
        })

    if domains_data:
        pd.DataFrame(domains_data).to_excel(writer, sheet_name='Domains', index=False)
        has_data = True

    # تصدير المواد المعرفية
    materials = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='material').all()
    materials_data = []

    for m in materials:
        parent = LevelDataEntry.query.get(m.parent_id) if m.parent_id else None
        subject_id = None
        subject_name = ''

        # الحصول على المادة الدراسية المرتبطة بالميدان
        if parent and parent.parent_id:
            subject = LevelDataEntry.query.get(parent.parent_id)
            if subject:
                subject_id = subject.id
                subject_name = subject.name

        materials_data.append({
            'id': m.id,
            'name': m.name,
            'description': m.description,
            'parent_id': m.parent_id,  # معرف الميدان
            'parent_name': parent.name if parent else '',  # اسم الميدان
            'domain_id': m.parent_id,
            'domain_name': parent.name if parent else '',
            'subject_id': subject_id,
            'subject_name': subject_name,
            'database_id': db_id,
            'level_id': database.level_id,
            'level_name': database.level.name if database.level else '',
            'is_active': m.is_active
        })

    if materials_data:
        pd.DataFrame(materials_data).to_excel(writer, sheet_name='Materials', index=False)
        has_data = True

    # تصدير الكفاءات
    competencies = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='competency').all()
    competencies_data = []

    for c in competencies:
        parent = LevelDataEntry.query.get(c.parent_id) if c.parent_id else None
        domain_id = None
        domain_name = ''
        subject_id = None
        subject_name = ''

        # الحصول على الميدان والمادة الدراسية المرتبطة بالمادة المعرفية
        if parent and parent.parent_id:
            domain = LevelDataEntry.query.get(parent.parent_id)
            if domain:
                domain_id = domain.id
                domain_name = domain.name
                if domain.parent_id:
                    subject = LevelDataEntry.query.get(domain.parent_id)
                    if subject:
                        subject_id = subject.id
                        subject_name = subject.name

        competencies_data.append({
            'id': c.id,
            'name': c.name,
            'description': c.description,
            'parent_id': c.parent_id,  # معرف المادة المعرفية
            'parent_name': parent.name if parent else '',  # اسم المادة المعرفية
            'material_id': c.parent_id,
            'material_name': parent.name if parent else '',
            'domain_id': domain_id,
            'domain_name': domain_name,
            'subject_id': subject_id,
            'subject_name': subject_name,
            'database_id': db_id,
            'level_id': database.level_id,
            'level_name': database.level.name if database.level else '',
            'is_active': c.is_active
        })

    if competencies_data:
        pd.DataFrame(competencies_data).to_excel(writer, sheet_name='Competencies', index=False)
        has_data = True

    # تصدير البيانات المترابطة في ورقة واحدة
    hierarchical_data = []

    # إضافة الكفاءات مع جميع العلاقات
    for c in competencies:
        parent_material = LevelDataEntry.query.get(c.parent_id) if c.parent_id else None
        domain_id = None
        domain_name = ''
        subject_id = None
        subject_name = ''

        if parent_material and parent_material.parent_id:
            domain = LevelDataEntry.query.get(parent_material.parent_id)
            if domain:
                domain_id = domain.id
                domain_name = domain.name
                if domain.parent_id:
                    subject = LevelDataEntry.query.get(domain.parent_id)
                    if subject:
                        subject_id = subject.id
                        subject_name = subject.name

        hierarchical_data.append({
            'level_id': database.level_id,
            'level_name': database.level.name if database.level else '',
            'subject_id': subject_id,
            'subject_name': subject_name,
            'domain_id': domain_id,
            'domain_name': domain_name,
            'material_id': c.parent_id,
            'material_name': parent_material.name if parent_material else '',
            'competency_id': c.id,
            'competency_name': c.name,
            'competency_description': c.description,
            'is_active': c.is_active
        })

    if hierarchical_data:
        pd.DataFrame(hierarchical_data).to_excel(writer, sheet_name='Hierarchical_Data', index=False)
        has_data = True

    # إذا لم تكن هناك أي بيانات للتصدير بخلاف معلومات قاعدة البيانات
    if not has_data:
        pd.DataFrame([{'info': 'لا توجد بيانات في قاعدة البيانات'}]).to_excel(writer, sheet_name='Info', index=False)

    writer.close()

    return redirect(url_for('static', filename=f'exports/{filename}'))

@app.route('/admin/import-all-levels-page', methods=['GET'])
@login_required
def import_all_levels_page():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('import_all_levels.html')

@app.route('/admin/generate-primary-database', methods=['GET'])
@login_required
def generate_primary_database():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إنشاء المستويات الخمسة للتعليم الابتدائي
        primary_levels = [
            'السنة الأولى ابتدائي',
            'السنة الثانية ابتدائي',
            'السنة الثالثة ابتدائي',
            'السنة الرابعة ابتدائي',
            'السنة الخامسة ابتدائي'
        ]

        # إنشاء المواد الدراسية الأساسية
        subjects = [
            'اللغة العربية',
            'الرياضيات',
            'التربية الإسلامية',
            'الفرنسية',
            'التربية العلمية',
            'التربية المدنية',
            'التربية الفنية/التشكيلية',
            'التاريخ',
            'الجغرافيا',
            'التربية البدنية',
            'حفظ القرآن',
            'الأمازيغية',
            'الإنجليزية'
        ]

        # إنشاء الميادين لكل مادة
        domains = {
            'اللغة العربية': [
                'فهم المنطوق',
                'التعبير الشفهي',
                'فهم المكتوب (ألعاب قرائية)',
                'التعبير الشفهي ( صيغ )',
                'التعبير الشفهي ( إنتاج)',
                'التعبير الكتابي (أركب)',
                'التعبير الكتابي (إنتاج كتابي)',
                'استكشاف الحرف',
                'كتابة الحرف',
                'إملاء',
                'التراكيب النحوية',
                'الصرف',
                'قراءة اجمالية',
                'قراءة (اداء و فهم)',
                'محفوظات',
                'المشاريع',
                'تطبيقات اللغة',
                'تصحيح التعبير الكتابي',
                'معالجة اللغة العربية',
                'تقويم فصلي في اللغة العربية'
            ],
            'الرياضيات': [
                'الاعداد و الحساب',
                'الفضاء و الهندسة',
                'المقادير و القياس',
                'تنظيم المعطيات',
                'تطبيقات الرياضيات',
                'معالجة الرياضيات',
                'تقويم فصلي في الرياضيات'
            ],
            'التربية الإسلامية': [
                'القرآن والحديث',
                'تهذيب السلوك',
                'مبادئ في العقيدة',
                'مبادئ في السيرة',
                'استعراض النص الشرعي',
                'تقويم فصلي في التربية الإسلامية'
            ],
            'التربية العلمية': [
                'الإنسان و الصحة',
                'الإنسان و المحيط',
                'المعلمة في الفضاء و الزمن',
                'المادة و علم الأشياء',
                'تقويم فصلي في التربية العلمية'
            ],
            'التربية المدنية': [
                'الحياة الجماعية',
                'الحياة المدنية',
                'الحياة الديمقراطية و المؤسسات',
                'تقويم فصلي في التربية المدنية'
            ],
            'التربية الفنية/التشكيلية': [
                'الرسم و التلوين',
                'فن التصميم',
                'النشيد و الأغنية التربوية',
                'التذوق الموسيقي و الاستماع',
                'القواعد الموسيقية و النظريات',
                'تقويم فصلي التربية الفنية'
            ],
            'التاريخ': [
                'أدوات و مفاهيم مادة التاريخ',
                'التاريخ العام',
                'التاريخ الوطني',
                'تقويم فصلي في التاريخ و الغرافيا'
            ],
            'الجغرافيا': [
                'أدوات و مفاهيم مادة الجغرافيا',
                'السكان و التنمية',
                'السكان و البيئة',
                'تقويم فصلي في التاريخ و الجغرافيا'
            ],
            'التربية البدنية': [
                'الوضعيات و التنقلات',
                'الحركات القاعدية',
                'الهيكلة و البناء',
                'تقويم فصلي في التربية البدنية'
            ],
            'الأمازيغية': [
                'ⵜⴰⵎⴰⵣⵉⴳⵀ'
            ],
            # المواد بدون ميادين محددة - ميدان واحد باسم المادة
            'الفرنسية': ['الفرنسية'],
            'حفظ القرآن': ['حفظ القرآن'],
            'الإنجليزية': ['الإنجليزية']
        }

        # إنشاء المستويات التعليمية في قاعدة البيانات
        for level_name in primary_levels:
            # التحقق من وجود المستوى
            level = EducationalLevel.query.filter_by(name=level_name).first()
            if not level:
                level = EducationalLevel(name=level_name)
                db.session.add(level)
                db.session.flush()  # للحصول على معرف المستوى

            # التحقق من وجود قاعدة بيانات نشطة لهذا المستوى
            database = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
            if not database:
                # إنشاء قاعدة بيانات جديدة للمستوى
                db_name = f"{level_name}_DB"
                database = LevelDatabase(
                    name=db_name,
                    level_id=level.id,
                    file_path=f"databases/{db_name}.db",
                    is_active=True
                )
                db.session.add(database)
                db.session.flush()  # للحصول على معرف قاعدة البيانات

            # إنشاء المواد الدراسية لهذا المستوى
            for subject_name in subjects:
                # التحقق من وجود المادة الدراسية
                subject = LevelDataEntry.query.filter_by(
                    database_id=database.id,
                    entry_type='subject',
                    name=subject_name
                ).first()

                if not subject:
                    subject = LevelDataEntry(
                        database_id=database.id,
                        entry_type='subject',
                        name=subject_name,
                        is_active=True
                    )
                    db.session.add(subject)
                    db.session.flush()

                # إنشاء الميادين لهذه المادة
                subject_domains = domains.get(subject_name, [subject_name])  # إذا لم توجد ميادين محددة، استخدم اسم المادة كميدان

                for domain_name in subject_domains:
                    # التحقق من وجود الميدان
                    domain = LevelDataEntry.query.filter_by(
                        database_id=database.id,
                        entry_type='domain',
                        name=domain_name,
                        parent_id=subject.id
                    ).first()

                    if not domain:
                        domain = LevelDataEntry(
                            database_id=database.id,
                            entry_type='domain',
                            name=domain_name,
                            parent_id=subject.id,
                            is_active=True
                        )
                        db.session.add(domain)
                        db.session.flush()

                    # إنشاء مواد معرفية افتراضية لكل ميدان
                    for i in range(1, 4):  # إنشاء 3 مواد معرفية لكل ميدان
                        material_name = f"{domain_name} - المادة المعرفية {i}"

                        # التحقق من وجود المادة المعرفية
                        material = LevelDataEntry.query.filter_by(
                            database_id=database.id,
                            entry_type='material',
                            name=material_name,
                            parent_id=domain.id
                        ).first()

                        if not material:
                            material = LevelDataEntry(
                                database_id=database.id,
                                entry_type='material',
                                name=material_name,
                                parent_id=domain.id,
                                is_active=True
                            )
                            db.session.add(material)
                            db.session.flush()

                        # إنشاء كفاءات مستهدفة لكل مادة معرفية
                        for j in range(1, 3):  # إنشاء كفاءتين لكل مادة معرفية
                            competency_name = f"الكفاءة {j} لـ {material_name}"
                            competency_description = f"وصف الكفاءة {j} لـ {material_name}"

                            # التحقق من وجود الكفاءة
                            competency = LevelDataEntry.query.filter_by(
                                database_id=database.id,
                                entry_type='competency',
                                name=competency_name,
                                parent_id=material.id
                            ).first()

                            if not competency:
                                competency = LevelDataEntry(
                                    database_id=database.id,
                                    entry_type='competency',
                                    name=competency_name,
                                    description=competency_description,
                                    parent_id=material.id,
                                    is_active=True
                                )
                                db.session.add(competency)

        # حفظ التغييرات
        db.session.commit()
        flash('تم إنشاء قواعد البيانات للمستويات الخمسة للتعليم الابتدائي بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء قواعد البيانات: {str(e)}', 'danger')

    return redirect(url_for('manage_databases'))

@app.route('/admin/import-all-levels', methods=['POST'])
@login_required
def import_all_levels():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    if 'file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('manage_databases'))

    file = request.files['file']

    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('manage_databases'))

    if not file.filename.endswith('.xlsx'):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx)', 'danger')
        return redirect(url_for('manage_databases'))

    try:
        # قراءة ملف Excel
        df = pd.read_excel(file, sheet_name=None)  # قراءة جميع الأوراق

        # استيراد المستويات
        if 'Levels' in df:
            levels_df = df['Levels']
            for _, row in levels_df.iterrows():
                row_dict = row.to_dict()
                level_name = row_dict.get('name', '')
                if level_name:
                    # التحقق من وجود المستوى
                    level = EducationalLevel.query.filter_by(name=level_name).first()
                    if not level:
                        level = EducationalLevel(name=level_name)
                        db.session.add(level)
                        db.session.flush()  # للحصول على معرف المستوى

                    # التحقق من وجود قاعدة بيانات نشطة لهذا المستوى
                    db_exists = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
                    if not db_exists:
                        # إنشاء قاعدة بيانات جديدة للمستوى
                        db_name = f"{level_name}_DB"
                        new_db = LevelDatabase(
                            name=db_name,
                            level_id=level.id,
                            file_path=f"databases/{db_name}.db",
                            is_active=True
                        )
                        db.session.add(new_db)
                        db.session.flush()  # للحصول على معرف قاعدة البيانات

        # حفظ التغييرات
        db.session.commit()
        flash('تم استيراد المستويات بنجاح', 'success')

        # استيراد البيانات لكل قاعدة بيانات
        if 'Hierarchical_Data' in df:
            hierarchical_data = df['Hierarchical_Data']

            # إنشاء قواميس لتخزين العناصر التي تم إنشاؤها
            created_subjects = {}
            created_domains = {}
            created_materials = {}

            for _, row in hierarchical_data.iterrows():
                row_dict = row.to_dict()

                # الحصول على المستوى
                level_name = row_dict.get('level_name', '')
                if not level_name:
                    continue

                level = EducationalLevel.query.filter_by(name=level_name).first()
                if not level:
                    continue

                # الحصول على قاعدة البيانات النشطة للمستوى
                database = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
                if not database:
                    continue

                # إنشاء المادة الدراسية إذا لم تكن موجودة
                subject_name = row_dict.get('subject_name', '')
                if not subject_name:
                    continue

                subject_key = f"{database.id}_{subject_name}"
                if subject_key not in created_subjects:
                    subject = LevelDataEntry.query.filter_by(
                        database_id=database.id,
                        entry_type='subject',
                        name=subject_name
                    ).first()

                    if not subject:
                        subject = LevelDataEntry(
                            database_id=database.id,
                            entry_type='subject',
                            name=subject_name,
                            is_active=True
                        )
                        db.session.add(subject)
                        db.session.flush()

                    created_subjects[subject_key] = subject.id

                subject_id = created_subjects[subject_key]

                # إنشاء الميدان إذا لم يكن موجوداً
                domain_name = row_dict.get('domain_name', '')
                if not domain_name:
                    continue

                domain_key = f"{database.id}_{domain_name}_{subject_id}"
                if domain_key not in created_domains:
                    domain = LevelDataEntry.query.filter_by(
                        database_id=database.id,
                        entry_type='domain',
                        name=domain_name,
                        parent_id=subject_id
                    ).first()

                    if not domain:
                        domain = LevelDataEntry(
                            database_id=database.id,
                            entry_type='domain',
                            name=domain_name,
                            parent_id=subject_id,
                            is_active=True
                        )
                        db.session.add(domain)
                        db.session.flush()

                    created_domains[domain_key] = domain.id

                domain_id = created_domains[domain_key]

                # إنشاء المادة المعرفية إذا لم تكن موجودة
                material_name = row_dict.get('material_name', '')
                if not material_name:
                    continue

                material_key = f"{database.id}_{material_name}_{domain_id}"
                if material_key not in created_materials:
                    material = LevelDataEntry.query.filter_by(
                        database_id=database.id,
                        entry_type='material',
                        name=material_name,
                        parent_id=domain_id
                    ).first()

                    if not material:
                        material = LevelDataEntry(
                            database_id=database.id,
                            entry_type='material',
                            name=material_name,
                            parent_id=domain_id,
                            is_active=True
                        )
                        db.session.add(material)
                        db.session.flush()

                    created_materials[material_key] = material.id

                material_id = created_materials[material_key]

                # إنشاء الكفاءة المستهدفة
                competency_name = row_dict.get('competency_name', '')
                competency_description = row_dict.get('competency_description', '')

                if not competency_name and not competency_description:
                    continue

                # التحقق من وجود الكفاءة
                competency = LevelDataEntry.query.filter_by(
                    database_id=database.id,
                    entry_type='competency',
                    name=competency_name,
                    parent_id=material_id
                ).first()

                if not competency:
                    competency = LevelDataEntry(
                        database_id=database.id,
                        entry_type='competency',
                        name=competency_name,
                        description=competency_description,
                        parent_id=material_id,
                        is_active=True
                    )
                    db.session.add(competency)

            # حفظ التغييرات
            db.session.commit()
            flash('تم استيراد البيانات الهرمية بنجاح', 'success')

        return redirect(url_for('manage_databases'))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')
        return redirect(url_for('manage_databases'))

@app.route('/admin/databases/<int:db_id>/import', methods=['POST'])
@login_required
def import_database_data(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if 'file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('view_database', db_id=db_id))

    file = request.files['file']
    import_type = request.form.get('import_type', 'all')
    clear_existing = 'clear_existing' in request.form

    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('view_database', db_id=db_id))

    if not file.filename.endswith('.xlsx'):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx)', 'danger')
        return redirect(url_for('view_database', db_id=db_id))

    try:
        # قراءة ملف Excel
        df = pd.read_excel(file, sheet_name=None)  # قراءة جميع الأوراق

        # حذف البيانات الحالية إذا تم تحديد ذلك
        if clear_existing:
            if import_type == 'all':
                # حذف جميع البيانات
                LevelDataEntry.query.filter_by(database_id=db_id).delete()
                db.session.commit()
                flash('تم حذف جميع البيانات الحالية', 'info')
            elif import_type in ['subject', 'domain', 'material', 'competency']:
                # حذف نوع محدد من البيانات
                LevelDataEntry.query.filter_by(database_id=db_id, entry_type=import_type).delete()
                db.session.commit()
                flash(f'تم حذف بيانات {import_type} الحالية', 'info')

        # استيراد البيانات المترابطة
        if import_type == 'hierarchical' and 'Hierarchical_Data' in df:
            hierarchical_data = df['Hierarchical_Data']

            # إنشاء قاموس لتخزين العناصر التي تم إنشاؤها
            created_subjects = {}
            created_domains = {}
            created_materials = {}

            for _, row in hierarchical_data.iterrows():
                row_dict = row.to_dict()

                # إنشاء المادة الدراسية إذا لم تكن موجودة
                subject_name = row_dict.get('subject_name', '')
                if subject_name and subject_name not in created_subjects:
                    subject = LevelDataEntry.query.filter_by(
                        database_id=db_id,
                        entry_type='subject',
                        name=subject_name
                    ).first()

                    if not subject:
                        subject = LevelDataEntry(
                            database_id=db_id,
                            entry_type='subject',
                            name=subject_name,
                            description='',
                            is_active=True
                        )
                        db.session.add(subject)
                        db.session.flush()  # للحصول على معرف العنصر

                    created_subjects[subject_name] = subject.id

                # إنشاء الميدان إذا لم يكن موجوداً
                domain_name = row_dict.get('domain_name', '')
                if domain_name and domain_name not in created_domains and subject_name in created_subjects:
                    domain = LevelDataEntry.query.filter_by(
                        database_id=db_id,
                        entry_type='domain',
                        name=domain_name
                    ).first()

                    if not domain:
                        domain = LevelDataEntry(
                            database_id=db_id,
                            entry_type='domain',
                            parent_id=created_subjects[subject_name],
                            name=domain_name,
                            description='',
                            is_active=True
                        )
                        db.session.add(domain)
                        db.session.flush()

                    created_domains[domain_name] = domain.id

                # إنشاء المادة المعرفية إذا لم تكن موجودة
                material_name = row_dict.get('material_name', '')
                if material_name and material_name not in created_materials and domain_name in created_domains:
                    material = LevelDataEntry.query.filter_by(
                        database_id=db_id,
                        entry_type='material',
                        name=material_name
                    ).first()

                    if not material:
                        material = LevelDataEntry(
                            database_id=db_id,
                            entry_type='material',
                            parent_id=created_domains[domain_name],
                            name=material_name,
                            description='',
                            is_active=True
                        )
                        db.session.add(material)
                        db.session.flush()

                    created_materials[material_name] = material.id

                # إنشاء الكفاءة المستهدفة
                competency_name = row_dict.get('competency_name', '')
                competency_description = row_dict.get('competency_description', '')
                if competency_name and material_name in created_materials:
                    competency = LevelDataEntry(
                        database_id=db_id,
                        entry_type='competency',
                        parent_id=created_materials[material_name],
                        name=competency_name,
                        description=competency_description,
                        is_active=True
                    )
                    db.session.add(competency)

            db.session.commit()
            flash('تم استيراد البيانات المترابطة بنجاح', 'success')
            return redirect(url_for('view_database', db_id=db_id))

        # استيراد نوع محدد من البيانات
        if import_type != 'all' and import_type != 'hierarchical':
            sheet_names = {
                'subject': ['Subjects', 'subjects'],
                'domain': ['Domains', 'domains'],
                'material': ['Materials', 'materials'],
                'competency': ['Competencies', 'competencies']
            }

            # البحث عن الورقة المناسبة
            sheet_found = False
            for sheet_name in sheet_names[import_type]:
                if sheet_name in df:
                    sheet_data = df[sheet_name]
                    sheet_found = True

                    for _, row in sheet_data.iterrows():
                        row_dict = row.to_dict()

                        # الحصول على معرف الأب
                        parent_id = None
                        if import_type != 'subject':
                            if 'parent_id' in row_dict and pd.notna(row_dict['parent_id']):
                                parent_id = int(row_dict['parent_id'])
                            elif 'parent_name' in row_dict and pd.notna(row_dict['parent_name']):
                                parent_name = row_dict['parent_name']
                                parent_type = {
                                    'domain': 'subject',
                                    'material': 'domain',
                                    'competency': 'material'
                                }[import_type]

                                parent = LevelDataEntry.query.filter_by(
                                    database_id=db_id,
                                    entry_type=parent_type,
                                    name=parent_name
                                ).first()

                                if parent:
                                    parent_id = parent.id

                        # إنشاء عنصر جديد
                        name = row_dict.get('name', '')
                        description = row_dict.get('description', '')
                        is_active = row_dict.get('is_active', True)

                        if name:
                            new_entry = LevelDataEntry(
                                database_id=db_id,
                                entry_type=import_type,
                                parent_id=parent_id,
                                name=name,
                                description=description,
                                is_active=is_active
                            )

                            db.session.add(new_entry)

                    db.session.commit()
                    flash(f'تم استيراد بيانات {import_type} بنجاح', 'success')
                    break

            if not sheet_found:
                flash(f'لم يتم العثور على ورقة {import_type} في الملف', 'warning')

            return redirect(url_for('view_database', db_id=db_id))

        # استيراد جميع البيانات
        if import_type == 'all':
            # معالجة كل ورقة
            for sheet_name, sheet_data in df.items():
                if sheet_name.lower() in ['subjects', 'domains', 'materials', 'competencies']:
                    entry_type = sheet_name.lower().rstrip('s')  # إزالة حرف 's' من النهاية

                    for _, row in sheet_data.iterrows():
                        # تحويل الصف إلى قاموس
                        row_dict = row.to_dict()

                        # الحصول على معرف الأب
                        parent_id = None
                        if entry_type != 'subject':
                            if 'parent_id' in row_dict and pd.notna(row_dict['parent_id']):
                                parent_id = int(row_dict['parent_id'])
                            elif 'parent_name' in row_dict and pd.notna(row_dict['parent_name']):
                                parent_name = row_dict['parent_name']
                                parent_type = {
                                    'domain': 'subject',
                                    'material': 'domain',
                                    'competency': 'material'
                                }[entry_type]

                                parent = LevelDataEntry.query.filter_by(
                                    database_id=db_id,
                                    entry_type=parent_type,
                                    name=parent_name
                                ).first()

                                if parent:
                                    parent_id = parent.id

                        # إنشاء عنصر جديد
                        name = row_dict.get('name', '')
                        description = row_dict.get('description', '')
                        is_active = row_dict.get('is_active', True)

                        if name:
                            new_entry = LevelDataEntry(
                                database_id=db_id,
                                entry_type=entry_type,
                                parent_id=parent_id,
                                name=name,
                                description=description,
                                is_active=is_active
                            )

                            db.session.add(new_entry)

            db.session.commit()
            flash('تم استيراد جميع البيانات بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')

    return redirect(url_for('view_database', db_id=db_id))

# إنشاء قاعدة البيانات وإضافة بيانات تجريبية
def create_sample_data():
    with app.app_context():
        # إنشاء الجداول
        db.create_all()

        # إضافة المستخدمين
        if User.query.count() == 0:
            admin = User(
                username='admin',
                email='<EMAIL>',
                password=generate_password_hash('admin123'),
                role=Role.ADMIN
            )
            inspector = User(
                username='inspector',
                email='<EMAIL>',
                password=generate_password_hash('inspector123'),
                role=Role.INSPECTOR
            )
            teacher1 = User(
                username='teacher',
                email='<EMAIL>',
                password=generate_password_hash('teacher123'),
                role=Role.TEACHER
            )
            teacher2 = User(
                username='teacher2',
                email='<EMAIL>',
                password=generate_password_hash('teacher123'),
                role=Role.TEACHER
            )

            db.session.add_all([admin, inspector, teacher1, teacher2])
            db.session.commit()

            # إضافة علاقة بين المفتش والأساتذة
            inspector.supervised_teachers.append(teacher1)
            inspector.supervised_teachers.append(teacher2)
            db.session.commit()

        # إضافة المستويات التعليمية
        if EducationalLevel.query.count() == 0:
            levels = [
                EducationalLevel(name='السنة الأولى ابتدائي', is_active=True, database_prefix='level1'),
                EducationalLevel(name='السنة الثانية ابتدائي', is_active=True, database_prefix='level2'),
                EducationalLevel(name='السنة الثالثة ابتدائي', is_active=True, database_prefix='level3'),
                EducationalLevel(name='السنة الرابعة ابتدائي', is_active=True, database_prefix='level4'),
                EducationalLevel(name='السنة الخامسة ابتدائي', is_active=True, database_prefix='level5')
            ]

            db.session.add_all(levels)
            db.session.commit()

        # إضافة قواعد البيانات المنفصلة
        if LevelDatabase.query.count() == 0:
            data_dir = os.path.join(app.root_path, 'data')
            os.makedirs(data_dir, exist_ok=True)

            for level in EducationalLevel.query.all():
                db_name = f"level_{level.id}_db"
                file_path = f"data/{db_name}.sqlite"

                new_db = LevelDatabase(
                    level_id=level.id,
                    name=f"قاعدة بيانات {level.name}",
                    file_path=file_path,
                    is_active=True
                )
                db.session.add(new_db)

            db.session.commit()

        print("تم إنشاء البيانات التجريبية بنجاح!")

# إدارة الإشعارات بين الإدارة والمفتشين
@app.route('/admin/notifications')
@login_required
def admin_notifications():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    notifications = AdminInspectorNotification.query.filter_by(sender_id=current_user.id).order_by(AdminInspectorNotification.created_at.desc()).paginate(
        page=page, per_page=app.config['NOTIFICATIONS_PER_PAGE'])

    # الحصول على قائمة المفتشين
    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()

    return render_template('admin_notifications.html', notifications=notifications, inspectors=inspectors)

@app.route('/admin/notifications/send', methods=['POST'])
@login_required
def send_admin_notification():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    receiver_id = request.form.get('receiver_id')
    title = request.form.get('title')
    message = request.form.get('message')

    if not receiver_id or not title or not message:
        flash('جميع الحقول مطلوبة', 'danger')
        return redirect(url_for('admin_notifications'))

    # التحقق من أن المستلم هو مفتش
    receiver = User.query.get(receiver_id)
    if not receiver or receiver.role != Role.INSPECTOR:
        flash('المستلم غير صالح', 'danger')
        return redirect(url_for('admin_notifications'))

    notification = AdminInspectorNotification(
        sender_id=current_user.id,
        receiver_id=receiver_id,
        title=title,
        message=message
    )

    db.session.add(notification)
    db.session.commit()

    flash('تم إرسال الإشعار بنجاح', 'success')
    return redirect(url_for('admin_notifications'))

@app.route('/inspector/notifications')
@login_required
def inspector_notifications():
    if current_user.role != Role.INSPECTOR:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)

    # الإشعارات الواردة من الإدارة
    admin_notifications = AdminInspectorNotification.query.filter_by(receiver_id=current_user.id).order_by(AdminInspectorNotification.created_at.desc()).paginate(
        page=page, per_page=app.config['NOTIFICATIONS_PER_PAGE'])

    # الإشعارات المرسلة للأساتذة
    sent_notifications = InspectorTeacherNotification.query.filter_by(sender_id=current_user.id).order_by(InspectorTeacherNotification.created_at.desc()).paginate(
        page=page, per_page=app.config['NOTIFICATIONS_PER_PAGE'])

    # الحصول على قائمة الأساتذة الذين يشرف عليهم المفتش
    teachers = current_user.supervised_teachers.all()

    return render_template('inspector_notifications.html', admin_notifications=admin_notifications, sent_notifications=sent_notifications, teachers=teachers)

@app.route('/inspector/notifications/send', methods=['POST'])
@login_required
def send_inspector_notification():
    if current_user.role != Role.INSPECTOR:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    receiver_id = request.form.get('receiver_id')
    title = request.form.get('title')
    message = request.form.get('message')

    if not receiver_id or not title or not message:
        flash('جميع الحقول مطلوبة', 'danger')
        return redirect(url_for('inspector_notifications'))

    # التحقق من أن المستلم هو أستاذ يشرف عليه المفتش
    receiver = User.query.get(receiver_id)
    if not receiver or receiver.role != Role.TEACHER or receiver not in current_user.supervised_teachers:
        flash('المستلم غير صالح', 'danger')
        return redirect(url_for('inspector_notifications'))

    notification = InspectorTeacherNotification(
        sender_id=current_user.id,
        receiver_id=receiver_id,
        title=title,
        message=message
    )

    db.session.add(notification)
    db.session.commit()

    flash('تم إرسال الإشعار بنجاح', 'success')
    return redirect(url_for('inspector_notifications'))

@app.route('/teacher/notifications')
@login_required
def teacher_notifications():
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    notifications = InspectorTeacherNotification.query.filter_by(receiver_id=current_user.id).order_by(InspectorTeacherNotification.created_at.desc()).paginate(
        page=page, per_page=app.config['NOTIFICATIONS_PER_PAGE'])

    return render_template('teacher_notifications.html', notifications=notifications)

@app.route('/notifications/mark-read/<notification_type>/<int:notification_id>')
@login_required
def mark_notification_read(notification_type, notification_id):
    if notification_type == 'admin_inspector':
        notification = AdminInspectorNotification.query.get_or_404(notification_id)
        if notification.receiver_id != current_user.id:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))
    elif notification_type == 'inspector_teacher':
        notification = InspectorTeacherNotification.query.get_or_404(notification_id)
        if notification.receiver_id != current_user.id:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))
    else:
        flash('نوع الإشعار غير صالح', 'danger')
        return redirect(url_for('dashboard'))

    notification.is_read = True
    db.session.commit()

    flash('تم تحديد الإشعار كمقروء', 'success')

    # التحقق من وجود معلمة الرجوع للصفحة السابقة
    referrer = request.referrer
    if referrer and 'dashboard/teacher' in referrer:
        return redirect(url_for('teacher_dashboard'))

    if current_user.role == Role.INSPECTOR:
        return redirect(url_for('inspector_notifications'))
    elif current_user.role == Role.TEACHER:
        return redirect(url_for('teacher_notifications'))
    else:
        return redirect(url_for('dashboard'))

# دالة مساعدة للحصول على عدد الإشعارات غير المقروءة
def get_unread_notifications_count(user):
    if user.role == Role.ADMIN:
        return 0  # الإدارة لا تتلقى إشعارات في النظام الحالي
    elif user.role == Role.INSPECTOR:
        return AdminInspectorNotification.query.filter_by(receiver_id=user.id, is_read=False).count()
    elif user.role == Role.TEACHER:
        return InspectorTeacherNotification.query.filter_by(receiver_id=user.id, is_read=False).count()
    return 0

# تعديل دالة context_processor لإضافة عدد الإشعارات غير المقروءة
@app.context_processor
def inject_unread_notifications():
    if current_user.is_authenticated:
        unread_count = get_unread_notifications_count(current_user)
        return {'unread_notifications_count': unread_count}
    return {'unread_notifications_count': 0}

# تحضير خطة درس باستخدام الذكاء الاصطناعي
@app.route('/prepare-lesson/<int:entry_id>')
@login_required
def prepare_lesson(entry_id):
    # التحقق من أن المستخدم هو أستاذ
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على سجل التقدم
    entry = ProgressEntry.query.get_or_404(entry_id)

    # التحقق من أن السجل ينتمي للأستاذ الحالي
    if entry.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المعلومات اللازمة لتحضير الدرس
    level_name = 'غير محدد'
    subject_name = 'غير محدد'
    domain_name = 'غير محدد'
    material_name = 'غير محدد'

    # الحصول على المعلومات من قاعدة البيانات الجديدة
    if entry.competency:
        # الحصول على الكفاءة
        competency = entry.competency

        # الحصول على المادة المعرفية
        knowledge_material = KnowledgeMaterial.query.get(competency.knowledge_material_id)
        if knowledge_material:
            material_name = knowledge_material.name

            # الحصول على الميدان
            domain = Domain.query.get(knowledge_material.domain_id)
            if domain:
                domain_name = domain.name

                # الحصول على المادة
                subject = Subject.query.get(domain.subject_id)
                if subject:
                    subject_name = subject.name

                    # الحصول على المستوى
                    level = EducationalLevel.query.get(subject.level_id)
                    if level:
                        level_name = level.name

    # الحصول على المعلومات من قاعدة البيانات القديمة (إذا لم تكن موجودة في الجديدة)
    if level_name == 'غير محدد' and entry.competency_id:
        try:
            # محاولة الحصول على المعلومات من قاعدة البيانات القديمة
            # التحقق من وجود المعلومات في قاعدة البيانات القديمة
            competency_entry = LevelDataEntry.query.filter_by(id=entry.competency_id, entry_type='competency').first()
            if competency_entry:
                material_entry = LevelDataEntry.query.filter_by(id=competency_entry.parent_id, entry_type='material').first()
                if material_entry:
                    material_name = material_entry.name

                    domain_entry = LevelDataEntry.query.filter_by(id=material_entry.parent_id, entry_type='domain').first()
                    if domain_entry:
                        domain_name = domain_entry.name

                        subject_entry = LevelDataEntry.query.filter_by(id=domain_entry.parent_id, entry_type='subject').first()
                        if subject_entry:
                            subject_name = subject_entry.name

                            # الحصول على المستوى من قاعدة البيانات
                            db = LevelDatabase.query.filter_by(id=subject_entry.database_id).first()
                            if db and db.level_id:
                                level = EducationalLevel.query.get(db.level_id)
                                if level:
                                    level_name = level.name
        except Exception as e:
            # في حالة حدوث خطأ، نستمر بالقيم الافتراضية
            print(f"Error retrieving data from old database: {e}")
            pass

    # الحصول على نص الكفاءة
    competency_text = 'كفاءة غير معروفة'
    if entry.competency:
        if entry.competency.description:
            competency_text = entry.competency.description
        elif entry.competency.name:
            competency_text = entry.competency.name
        else:
            competency_text = 'بدون وصف'
    else:
        competency_text = 'كفاءة غير معروفة'

    # إعداد المعلومات للعرض في الصفحة
    lesson_info = {
        'level': level_name,
        'subject': subject_name,
        'domain': domain_name,
        'material': material_name,
        'competency': competency_text,
        'entry_id': entry_id
    }

    return render_template('prepare_lesson.html', lesson_info=lesson_info)

# إضافة أستاذ تحت الإشراف
@app.route('/inspector/add-teacher', methods=['POST'])
@login_required
def add_supervised_teacher():
    if current_user.role != Role.INSPECTOR:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    teacher_id = request.form.get('teacher_id')
    if not teacher_id:
        flash('يرجى اختيار أستاذ', 'danger')
        return redirect(url_for('inspector_dashboard'))

    # التحقق من وجود الأستاذ
    teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
    if not teacher:
        flash('الأستاذ غير موجود', 'danger')
        return redirect(url_for('inspector_dashboard'))

    # التحقق من أن الأستاذ ليس تحت إشراف المفتش بالفعل
    if teacher in current_user.supervised_teachers:
        flash('الأستاذ بالفعل تحت إشرافك', 'warning')
        return redirect(url_for('inspector_dashboard'))

    # إضافة الأستاذ تحت إشراف المفتش
    current_user.supervised_teachers.append(teacher)
    db.session.commit()

    # إرسال إشعار للأستاذ
    notification = InspectorTeacherNotification(
        sender_id=current_user.id,
        receiver_id=teacher.id,
        title='تم إضافتك تحت إشراف مفتش جديد',
        message=f'مرحباً! لقد تم إضافتك تحت إشراف المفتش {current_user.username}. يمكنك الآن مشاركة تقدمك والتواصل معه من خلال نظام الإشعارات.'
    )
    db.session.add(notification)
    db.session.commit()

    flash(f'تمت إضافة الأستاذ {teacher.username} تحت إشرافك بنجاح', 'success')
    return redirect(url_for('inspector_dashboard'))

# تعديل التقدم
@app.route('/progress/edit/<int:entry_id>', methods=['GET', 'POST'])
@login_required
def edit_progress(entry_id):
    # السماح للمعلمين والمفتشين بتعديل التقدم
    if current_user.role not in [Role.TEACHER, Role.INSPECTOR]:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = ProgressEntry.query.get_or_404(entry_id)

    # التحقق من الصلاحيات
    if current_user.role == Role.TEACHER and entry.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))
    elif current_user.role == Role.INSPECTOR:
        # التحقق من أن المعلم تحت إشراف المفتش
        teacher = User.query.get(entry.user_id)
        if teacher not in current_user.supervised_teachers:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # تحديث حالة التقدم
        entry.status = request.form.get('status')
        entry.notes = request.form.get('notes')
        entry.date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        # محاولة استخدام datetime.now مع timezone.utc
        try:
            entry.updated_at = datetime.now(timezone.utc)
        except (AttributeError, TypeError):
            # إذا فشلت، استخدم datetime.utcnow
            entry.updated_at = datetime.utcnow()

        # تحديث العلاقات إذا تم توفيرها
        if request.form.get('level_id'):
            entry.level_id = request.form.get('level_id')
        if request.form.get('subject_id'):
            entry.subject_id = request.form.get('subject_id')
        if request.form.get('domain_id'):
            entry.domain_id = request.form.get('domain_id')
        if request.form.get('material_id'):
            entry.material_id = request.form.get('material_id')
        if request.form.get('competency_id'):
            entry.competency_id = request.form.get('competency_id')

        db.session.commit()
        flash('تم تحديث التقدم بنجاح', 'success')

        # إعادة توجيه المستخدم حسب دوره
        if current_user.role == Role.TEACHER:
            return redirect(url_for('teacher_dashboard'))
        else:
            return redirect(url_for('inspector_dashboard'))

    # الحصول على البيانات اللازمة للقائمة المنسدلة
    levels = EducationalLevel.query.all()

    # الحصول على المواد والميادين والمواد المعرفية والكفاءات
    subjects = LevelDataEntry.query.filter_by(entry_type='subject').all()
    domains = LevelDataEntry.query.filter_by(entry_type='domain').all()
    materials = LevelDataEntry.query.filter_by(entry_type='material').all()
    competencies = Competency.query.all()

    return render_template('edit_progress.html',
                           entry=entry,
                           levels=levels,
                           subjects=subjects,
                           domains=domains,
                           materials=materials,
                           competencies=competencies)

# حذف التقدم
@app.route('/progress/delete/<int:entry_id>')
@login_required
def delete_progress(entry_id):
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = ProgressEntry.query.get_or_404(entry_id)

    # التحقق من أن المستخدم هو صاحب التقدم
    if entry.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    db.session.delete(entry)
    db.session.commit()

    flash('تم حذف التقدم بنجاح', 'success')
    return redirect(url_for('teacher_dashboard'))

if __name__ == '__main__':
    create_sample_data()
    app.run(debug=True)




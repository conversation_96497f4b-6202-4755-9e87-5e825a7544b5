{% extends 'base.html' %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header bg-primary text-white">
                <h3 class="text-center font-weight-light my-2">تسجيل الدخول</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('login') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="form-floating mb-3">
                        <input class="form-control" id="username" name="username" type="text" placeholder="اسم المستخدم" required />
                        <label for="username">اسم المستخدم</label>
                    </div>
                    <div class="form-floating mb-3">
                        <input class="form-control" id="password" name="password" type="password" placeholder="كلمة المرور" required />
                        <label for="password">كلمة المرور</label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" id="remember" name="remember" type="checkbox" />
                        <label class="form-check-label" for="remember">تذكرني</label>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-primary btn-lg" type="submit">تسجيل الدخول</button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a href="{{ url_for('register') }}">ليس لديك حساب؟ سجل الآن!</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
